package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "bi_dashboard_export_tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BiDashboardExportTask {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "task_id", nullable = false, unique = true)
    private String taskId;
    
    @Column(name = "dashboard_id", nullable = false)
    private Long dashboardId;
    
    @Column(name = "dashboard_name", nullable = false)
    private String dashboardName;
    
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private ExportStatus status;
    
    @Column(name = "progress")
    private Integer progress = 0;
    
    @Column(name = "total_resources")
    private Integer totalResources = 0;
    
    @Column(name = "processed_resources")
    private Integer processedResources = 0;
    
    @Column(name = "export_file_path")
    private String exportFilePath;
    
    @Column(name = "export_file_name")
    private String exportFileName;
    
    @Column(name = "export_file_size")
    private Long exportFileSize;
    
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    @Column(name = "resource_summary", columnDefinition = "TEXT")
    private String resourceSummary; // JSON格式的资源统计信息
    
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt; // 导出文件过期时间
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        // 设置导出文件7天后过期
        expiresAt = LocalDateTime.now().plusDays(7);
    }
    
    // 导出状态枚举
    public enum ExportStatus {
        PENDING("待处理"),
        SCANNING("扫描资源"),
        COLLECTING("收集文件"),
        PACKAGING("打包中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        EXPIRED("已过期");
        
        private final String displayName;
        
        ExportStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    // 检查是否已过期
    @Transient
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    // 更新进度
    public void updateProgress(int processed, int total, String currentStep) {
        this.processedResources = processed;
        this.totalResources = total;
        if (total > 0) {
            this.progress = (int) ((processed * 100.0) / total);
        }
    }
}
