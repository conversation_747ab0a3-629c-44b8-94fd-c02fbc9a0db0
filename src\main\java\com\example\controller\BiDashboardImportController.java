package com.example.controller;

import com.example.dto.ImportRequest;
import com.example.dto.ImportResult;
import com.example.service.BiDashboardImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/bi/dashboard/import")
@Slf4j
public class BiDashboardImportController {
    
    @Autowired
    private BiDashboardImportService importService;
    
    /**
     * 预览导入包信息
     */
    @PostMapping("/preview")
    public ResponseEntity<Map<String, Object>> previewImportPackage(
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("预览导入包，文件: {}", file.getOriginalFilename());
            
            BiDashboardImportService.ImportPreview preview = importService.previewImportPackage(file);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "预览成功");
            response.put("data", preview);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("预览导入包失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "预览失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * 预览导入包信息（从文件路径）
     */
    @PostMapping("/preview/path")
    public ResponseEntity<Map<String, Object>> previewImportPackageFromPath(
            @RequestParam String filePath) {
        try {
            log.info("预览导入包，文件路径: {}", filePath);
            
            BiDashboardImportService.ImportPreview preview = importService.previewImportPackageFromPath(filePath);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "预览成功");
            response.put("data", preview);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("预览导入包失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "预览失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * 导入大屏（从上传文件）
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> importFromFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false) String newDashboardName,
            @RequestParam(defaultValue = "false") boolean overwriteExisting,
            @RequestParam(defaultValue = "true") boolean restoreResources,
            @RequestParam(defaultValue = "true") boolean validateResources,
            @RequestParam(required = false) String resourcePathPrefix,
            @RequestParam(defaultValue = "SKIP") ImportRequest.ConflictResolution conflictResolution) {
        
        try {
            log.info("导入大屏，文件: {}", file.getOriginalFilename());
            
            // 构建导入请求
            ImportRequest request = new ImportRequest();
            request.setNewDashboardName(newDashboardName);
            request.setOverwriteExisting(overwriteExisting);
            request.setRestoreResources(restoreResources);
            request.setValidateResources(validateResources);
            request.setResourcePathPrefix(resourcePathPrefix);
            request.setConflictResolution(conflictResolution);
            
            ImportResult result = importService.importFromFile(file, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("导入大屏失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导入失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 导入大屏（从文件路径）
     */
    @PostMapping("/path")
    public ResponseEntity<Map<String, Object>> importFromPath(@Valid @RequestBody ImportRequest request) {
        try {
            log.info("导入大屏，文件路径: {}", request.getImportFilePath());
            
            ImportResult result = importService.importFromPath(request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("导入大屏失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "导入失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 批量导入大屏
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchImport(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(defaultValue = "false") boolean overwriteExisting,
            @RequestParam(defaultValue = "true") boolean restoreResources,
            @RequestParam(defaultValue = "SKIP") ImportRequest.ConflictResolution conflictResolution) {
        
        try {
            log.info("批量导入大屏，文件数量: {}", files.length);
            
            Map<String, ImportResult> results = new HashMap<>();
            int successCount = 0;
            int failureCount = 0;
            
            for (MultipartFile file : files) {
                try {
                    ImportRequest request = new ImportRequest();
                    request.setOverwriteExisting(overwriteExisting);
                    request.setRestoreResources(restoreResources);
                    request.setConflictResolution(conflictResolution);
                    
                    ImportResult result = importService.importFromFile(file, request);
                    results.put(file.getOriginalFilename(), result);
                    
                    if (result.isSuccess()) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                    
                } catch (Exception e) {
                    log.error("批量导入单个文件失败: {}", file.getOriginalFilename(), e);
                    ImportResult failureResult = ImportResult.failure("导入失败: " + e.getMessage(), 
                                                                     java.util.Arrays.asList(e.getMessage()));
                    results.put(file.getOriginalFilename(), failureResult);
                    failureCount++;
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", failureCount == 0);
            response.put("message", String.format("批量导入完成，成功: %d, 失败: %d", successCount, failureCount));
            response.put("data", results);
            response.put("summary", Map.of(
                "total", files.length,
                "success", successCount,
                "failure", failureCount
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量导入失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量导入失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 验证导入文件
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateImportFile(
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("验证导入文件: {}", file.getOriginalFilename());
            
            // 通过预览来验证文件
            BiDashboardImportService.ImportPreview preview = importService.previewImportPackage(file);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "文件验证通过");
            response.put("valid", true);
            response.put("preview", preview);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("验证导入文件失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "文件验证失败: " + e.getMessage());
            response.put("valid", false);
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * 获取导入配置选项
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getImportConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 冲突解决策略
            config.put("conflictResolutions", java.util.Arrays.asList(
                Map.of("value", "SKIP", "label", "跳过", "description", "跳过已存在的资源文件"),
                Map.of("value", "OVERWRITE", "label", "覆盖", "description", "覆盖已存在的资源文件"),
                Map.of("value", "RENAME", "label", "重命名", "description", "重命名冲突的资源文件")
            ));
            
            // 默认配置
            config.put("defaults", Map.of(
                "overwriteExisting", false,
                "restoreResources", true,
                "validateResources", true,
                "conflictResolution", "SKIP"
            ));
            
            // 限制信息
            config.put("limits", Map.of(
                "maxFileSize", "100MB",
                "supportedFormats", java.util.Arrays.asList("ZIP"),
                "maxBatchFiles", 10
            ));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取导入配置失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取导入配置失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
