package com.example.service;

import com.example.dto.ExportRequest;
import com.example.dto.ExportResult;
import com.example.entity.BiDashboard;
import com.example.entity.BiDashboardExportTask;
import com.example.repository.BiDashboardExportTaskRepository;
import com.example.repository.BiDashboardRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BiDashboardExportService {
    
    @Autowired
    private BiDashboardRepository dashboardRepository;
    
    @Autowired
    private BiDashboardExportTaskRepository exportTaskRepository;
    
    @Autowired
    private ResourceCollectorService resourceCollectorService;
    
    @Autowired
    private PackageBuilderService packageBuilderService;
    
    /**
     * 开始导出大屏
     */
    @Transactional
    public ExportResult startExport(ExportRequest request) {
        log.info("开始导出大屏，ID: {}", request.getDashboardId());
        
        // 1. 验证大屏是否存在
        Optional<BiDashboard> dashboardOpt = dashboardRepository.findById(request.getDashboardId());
        if (!dashboardOpt.isPresent()) {
            throw new RuntimeException("大屏不存在，ID: " + request.getDashboardId());
        }
        
        BiDashboard dashboard = dashboardOpt.get();
        
        // 2. 创建导出任务
        BiDashboardExportTask exportTask = createExportTask(dashboard, request);
        exportTask = exportTaskRepository.save(exportTask);
        
        // 3. 异步执行导出
        executeExportAsync(dashboard, exportTask);
        
        // 4. 返回任务信息
        return ExportResult.fromTask(exportTask);
    }
    
    /**
     * 创建导出任务
     */
    private BiDashboardExportTask createExportTask(BiDashboard dashboard, ExportRequest request) {
        BiDashboardExportTask task = new BiDashboardExportTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setDashboardId(dashboard.getId());
        task.setDashboardName(dashboard.getName());
        task.setStatus(BiDashboardExportTask.ExportStatus.PENDING);
        
        return task;
    }
    
    /**
     * 异步执行导出
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> executeExportAsync(BiDashboard dashboard, BiDashboardExportTask exportTask) {
        try {
            log.info("开始异步导出任务: {}", exportTask.getTaskId());
            
            // 1. 更新状态为扫描资源
            exportTask.setStatus(BiDashboardExportTask.ExportStatus.SCANNING);
            exportTaskRepository.save(exportTask);
            
            // 2. 收集资源
            ResourceCollectorService.ResourceCollection resources = 
                resourceCollectorService.collectResources(dashboard);
            
            // 3. 更新状态为收集文件
            exportTask.setStatus(BiDashboardExportTask.ExportStatus.COLLECTING);
            exportTask.setTotalResources(resources.getTotalCount());
            exportTaskRepository.save(exportTask);
            
            // 4. 构建导出包
            String exportFilePath = packageBuilderService.buildExportPackage(dashboard, resources, exportTask);
            
            log.info("导出任务完成: {}", exportTask.getTaskId());
            
        } catch (Exception e) {
            log.error("导出任务失败: {}", exportTask.getTaskId(), e);
            
            // 更新任务失败状态
            exportTask.setStatus(BiDashboardExportTask.ExportStatus.FAILED);
            exportTask.setErrorMessage(e.getMessage());
            exportTask.setCompletedAt(LocalDateTime.now());
            exportTaskRepository.save(exportTask);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 查询导出任务状态
     */
    public ExportResult getExportStatus(String taskId) {
        Optional<BiDashboardExportTask> taskOpt = exportTaskRepository.findByTaskId(taskId);
        if (!taskOpt.isPresent()) {
            throw new RuntimeException("导出任务不存在，ID: " + taskId);
        }
        
        BiDashboardExportTask task = taskOpt.get();
        
        // 检查是否过期
        if (task.isExpired()) {
            task.setStatus(BiDashboardExportTask.ExportStatus.EXPIRED);
            exportTaskRepository.save(task);
        }
        
        return ExportResult.fromTask(task);
    }
    
    /**
     * 获取大屏的所有导出任务
     */
    public List<ExportResult> getDashboardExportTasks(Long dashboardId) {
        List<BiDashboardExportTask> tasks = exportTaskRepository.findByDashboardIdOrderByCreatedAtDesc(dashboardId);
        
        return tasks.stream()
                   .map(ExportResult::fromTask)
                   .collect(Collectors.toList());
    }
    
    /**
     * 获取最近的导出任务
     */
    public List<ExportResult> getRecentExportTasks(int limit) {
        List<BiDashboardExportTask> tasks = exportTaskRepository.findRecentTasks();
        
        return tasks.stream()
                   .limit(limit)
                   .map(ExportResult::fromTask)
                   .collect(Collectors.toList());
    }
    
    /**
     * 取消导出任务
     */
    @Transactional
    public boolean cancelExport(String taskId) {
        Optional<BiDashboardExportTask> taskOpt = exportTaskRepository.findByTaskId(taskId);
        if (!taskOpt.isPresent()) {
            return false;
        }
        
        BiDashboardExportTask task = taskOpt.get();
        
        // 只能取消未完成的任务
        if (task.getStatus() == BiDashboardExportTask.ExportStatus.PENDING ||
            task.getStatus() == BiDashboardExportTask.ExportStatus.SCANNING ||
            task.getStatus() == BiDashboardExportTask.ExportStatus.COLLECTING ||
            task.getStatus() == BiDashboardExportTask.ExportStatus.PACKAGING) {
            
            task.setStatus(BiDashboardExportTask.ExportStatus.FAILED);
            task.setErrorMessage("用户取消");
            task.setCompletedAt(LocalDateTime.now());
            exportTaskRepository.save(task);
            
            log.info("导出任务已取消: {}", taskId);
            return true;
        }
        
        return false;
    }
    
    /**
     * 删除导出任务和文件
     */
    @Transactional
    public boolean deleteExport(String taskId) {
        Optional<BiDashboardExportTask> taskOpt = exportTaskRepository.findByTaskId(taskId);
        if (!taskOpt.isPresent()) {
            return false;
        }
        
        BiDashboardExportTask task = taskOpt.get();
        
        try {
            // 删除导出文件
            if (task.getExportFilePath() != null) {
                java.nio.file.Path filePath = java.nio.file.Paths.get(task.getExportFilePath());
                if (java.nio.file.Files.exists(filePath)) {
                    java.nio.file.Files.delete(filePath);
                    log.info("删除导出文件: {}", task.getExportFilePath());
                }
            }
            
            // 删除任务记录
            exportTaskRepository.delete(task);
            
            log.info("删除导出任务: {}", taskId);
            return true;
            
        } catch (Exception e) {
            log.error("删除导出任务失败: {}", taskId, e);
            return false;
        }
    }
    
    /**
     * 清理过期的导出任务
     */
    @Transactional
    public int cleanupExpiredTasks() {
        List<BiDashboardExportTask> expiredTasks = exportTaskRepository.findExpiredTasks(LocalDateTime.now());
        
        int cleanedCount = 0;
        for (BiDashboardExportTask task : expiredTasks) {
            if (deleteExport(task.getTaskId())) {
                cleanedCount++;
            }
        }
        
        log.info("清理过期导出任务完成，清理数量: {}", cleanedCount);
        return cleanedCount;
    }
    
    /**
     * 清理旧的已完成任务（保留指定天数）
     */
    @Transactional
    public int cleanupOldCompletedTasks(int keepDays) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(keepDays);
        List<BiDashboardExportTask> oldTasks = exportTaskRepository.findCompletedTasksOlderThan(cutoffDate);
        
        int cleanedCount = 0;
        for (BiDashboardExportTask task : oldTasks) {
            if (deleteExport(task.getTaskId())) {
                cleanedCount++;
            }
        }
        
        log.info("清理旧的已完成任务完成，清理数量: {}", cleanedCount);
        return cleanedCount;
    }
    
    /**
     * 获取导出文件路径（用于下载）
     */
    public String getExportFilePath(String taskId) {
        Optional<BiDashboardExportTask> taskOpt = exportTaskRepository.findByTaskId(taskId);
        if (!taskOpt.isPresent()) {
            throw new RuntimeException("导出任务不存在，ID: " + taskId);
        }
        
        BiDashboardExportTask task = taskOpt.get();
        
        if (task.getStatus() != BiDashboardExportTask.ExportStatus.COMPLETED) {
            throw new RuntimeException("导出任务未完成，无法下载");
        }
        
        if (task.isExpired()) {
            throw new RuntimeException("导出文件已过期");
        }
        
        if (task.getExportFilePath() == null) {
            throw new RuntimeException("导出文件路径不存在");
        }
        
        // 验证文件是否存在
        java.nio.file.Path filePath = java.nio.file.Paths.get(task.getExportFilePath());
        if (!java.nio.file.Files.exists(filePath)) {
            throw new RuntimeException("导出文件不存在");
        }
        
        return task.getExportFilePath();
    }
}
