-- BI大屏导出导入功能数据库迁移脚本
-- 版本: V1.0.1
-- 描述: 创建大屏导出任务表和相关索引

-- 创建导出任务表
CREATE TABLE IF NOT EXISTS bi_dashboard_export_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '任务唯一标识',
    dashboard_id BIGINT NOT NULL COMMENT '大屏ID',
    dashboard_name VARCHAR(255) NOT NULL COMMENT '大屏名称',
    status VARCHAR(32) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态：PENDING,SCANNING,COLLECTING,PACKAGING,COMPLETED,FAILED,EXPIRED',
    progress INT NOT NULL DEFAULT 0 COMMENT '进度百分比(0-100)',
    current_step VARCHAR(100) COMMENT '当前执行步骤',
    
    -- 导出配置
    include_resources BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否包含资源文件',
    include_images BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否包含图片',
    include_materials BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否包含素材',
    include_videos BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否包含视频',
    
    -- 结果信息
    export_file_path VARCHAR(500) COMMENT '导出文件路径',
    export_file_size BIGINT COMMENT '导出文件大小(字节)',
    total_resources INT DEFAULT 0 COMMENT '总资源数量',
    successful_resources INT DEFAULT 0 COMMENT '成功处理的资源数量',
    failed_resources INT DEFAULT 0 COMMENT '失败的资源数量',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    error_details TEXT COMMENT '详细错误信息',
    
    -- 时间字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    started_at TIMESTAMP NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    
    -- 创建者信息
    created_by VARCHAR(100) COMMENT '创建者',
    
    INDEX idx_task_id (task_id),
    INDEX idx_dashboard_id (dashboard_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_dashboard_status (dashboard_id, status),
    INDEX idx_status_created (status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BI大屏导出任务表';

-- 创建导出任务资源详情表（可选，用于详细记录每个资源的处理情况）
CREATE TABLE IF NOT EXISTS bi_export_task_resource (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    resource_type VARCHAR(32) NOT NULL COMMENT '资源类型：image,material,video',
    resource_url VARCHAR(500) NOT NULL COMMENT '资源URL',
    resource_path VARCHAR(500) COMMENT '资源文件路径',
    file_name VARCHAR(255) COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小(字节)',
    status VARCHAR(32) NOT NULL DEFAULT 'PENDING' COMMENT '处理状态：PENDING,SUCCESS,FAILED,SKIPPED',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_resource_type (resource_type),
    INDEX idx_status (status),
    INDEX idx_task_status (task_id, status),
    FOREIGN KEY (task_id) REFERENCES bi_dashboard_export_task(task_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导出任务资源详情表';

-- 创建导入历史记录表（可选，用于记录导入操作历史）
CREATE TABLE IF NOT EXISTS bi_dashboard_import_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    import_id VARCHAR(64) NOT NULL UNIQUE COMMENT '导入唯一标识',
    original_dashboard_id BIGINT COMMENT '原始大屏ID（来自导出包）',
    new_dashboard_id BIGINT COMMENT '新创建的大屏ID',
    dashboard_name VARCHAR(255) NOT NULL COMMENT '大屏名称',
    
    -- 导入配置
    import_file_name VARCHAR(255) COMMENT '导入文件名',
    import_file_size BIGINT COMMENT '导入文件大小(字节)',
    overwrite_existing BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否覆盖现有大屏',
    restore_resources BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否恢复资源文件',
    conflict_resolution VARCHAR(32) NOT NULL DEFAULT 'SKIP' COMMENT '冲突解决策略：SKIP,OVERWRITE,RENAME',
    
    -- 结果信息
    status VARCHAR(32) NOT NULL DEFAULT 'SUCCESS' COMMENT '导入状态：SUCCESS,FAILED',
    total_resources INT DEFAULT 0 COMMENT '总资源数量',
    successful_resources INT DEFAULT 0 COMMENT '成功恢复的资源数量',
    skipped_resources INT DEFAULT 0 COMMENT '跳过的资源数量',
    failed_resources INT DEFAULT 0 COMMENT '失败的资源数量',
    
    -- 错误信息
    error_message TEXT COMMENT '错误信息',
    error_details TEXT COMMENT '详细错误信息',
    
    -- 时间字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '完成时间',
    
    -- 创建者信息
    created_by VARCHAR(100) COMMENT '创建者',
    
    INDEX idx_import_id (import_id),
    INDEX idx_original_dashboard_id (original_dashboard_id),
    INDEX idx_new_dashboard_id (new_dashboard_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_dashboard_name (dashboard_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BI大屏导入历史记录表';

-- 创建系统配置表（用于存储导出导入相关配置）
CREATE TABLE IF NOT EXISTS bi_system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(32) NOT NULL DEFAULT 'STRING' COMMENT '配置类型：STRING,INTEGER,BOOLEAN,JSON',
    description VARCHAR(500) COMMENT '配置描述',
    is_system BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为系统配置',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO bi_system_config (config_key, config_value, config_type, description, is_system) VALUES
('export.task.expire.hours', '24', 'INTEGER', '导出任务过期时间（小时）', TRUE),
('export.file.max.size.mb', '500', 'INTEGER', '导出文件最大大小（MB）', TRUE),
('export.concurrent.limit', '5', 'INTEGER', '并发导出任务限制', TRUE),
('import.file.max.size.mb', '100', 'INTEGER', '导入文件最大大小（MB）', TRUE),
('import.temp.cleanup.hours', '2', 'INTEGER', '导入临时文件清理时间（小时）', TRUE),
('resource.validation.enabled', 'true', 'BOOLEAN', '是否启用资源文件验证', TRUE),
('export.cleanup.old.days', '30', 'INTEGER', '清理旧导出任务的天数', TRUE),
('export.version', '1.0.0', 'STRING', '导出功能版本', TRUE)
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = CURRENT_TIMESTAMP;

-- 创建清理任务的存储过程（可选）
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanupExpiredExportTasks()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_count INT DEFAULT 0;
    
    -- 删除过期的导出任务
    DELETE FROM bi_dashboard_export_task 
    WHERE status IN ('COMPLETED', 'FAILED', 'EXPIRED') 
      AND expires_at < NOW();
    
    SET task_count = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO bi_system_config (config_key, config_value, config_type, description, is_system)
    VALUES (
        CONCAT('cleanup.last.run.', DATE_FORMAT(NOW(), '%Y%m%d')),
        CONCAT('清理过期任务: ', task_count, '个'),
        'STRING',
        '自动清理任务记录',
        TRUE
    ) ON DUPLICATE KEY UPDATE 
        config_value = VALUES(config_value),
        updated_at = CURRENT_TIMESTAMP;
        
END //

DELIMITER ;

-- 创建视图：导出任务统计
CREATE OR REPLACE VIEW v_export_task_stats AS
SELECT 
    DATE(created_at) as export_date,
    status,
    COUNT(*) as task_count,
    AVG(progress) as avg_progress,
    SUM(total_resources) as total_resources,
    SUM(successful_resources) as successful_resources,
    SUM(failed_resources) as failed_resources,
    AVG(export_file_size) as avg_file_size
FROM bi_dashboard_export_task
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), status
ORDER BY export_date DESC, status;

-- 创建视图：大屏导出统计
CREATE OR REPLACE VIEW v_dashboard_export_stats AS
SELECT 
    d.id as dashboard_id,
    d.name as dashboard_name,
    COUNT(t.id) as export_count,
    MAX(t.created_at) as last_export_time,
    SUM(CASE WHEN t.status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_exports,
    SUM(CASE WHEN t.status = 'FAILED' THEN 1 ELSE 0 END) as failed_exports
FROM bi_dashboard d
LEFT JOIN bi_dashboard_export_task t ON d.id = t.dashboard_id
GROUP BY d.id, d.name
ORDER BY export_count DESC;

-- 添加注释说明
ALTER TABLE bi_dashboard_export_task COMMENT = 'BI大屏导出任务表 - 记录所有导出任务的状态和进度';
ALTER TABLE bi_export_task_resource COMMENT = '导出任务资源详情表 - 记录每个资源文件的处理情况';
ALTER TABLE bi_dashboard_import_history COMMENT = 'BI大屏导入历史记录表 - 记录所有导入操作的历史';
ALTER TABLE bi_system_config COMMENT = '系统配置表 - 存储导出导入功能相关配置';
