package com.example.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportRequest {
    
    @NotBlank(message = "导入文件路径不能为空")
    private String importFilePath;
    
    private String newDashboardName; // 新大屏名称，如果为空则使用原名称
    
    private boolean overwriteExisting = false; // 是否覆盖同名大屏
    
    private boolean restoreResources = true; // 是否恢复资源文件
    
    private boolean validateResources = true; // 是否验证资源完整性
    
    private String resourcePathPrefix; // 资源路径前缀，用于自定义资源存储位置
    
    private ConflictResolution conflictResolution = ConflictResolution.SKIP; // 资源冲突处理策略
    
    // 资源冲突处理策略
    public enum ConflictResolution {
        SKIP("跳过"),
        OVERWRITE("覆盖"),
        RENAME("重命名");
        
        private final String displayName;
        
        ConflictResolution(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
