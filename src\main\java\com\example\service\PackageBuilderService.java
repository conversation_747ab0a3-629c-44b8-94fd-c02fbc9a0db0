package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiDashboardExportTask;
import com.example.repository.BiDashboardExportTaskRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class PackageBuilderService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private BiDashboardExportTaskRepository exportTaskRepository;
    
    @Value("${export.temp.path:temp/exports}")
    private String exportTempPath;
    
    @Value("${server.external-url:localhost:8080}")
    private String serverExternalUrl;
    
    /**
     * 构建导出包
     */
    public String buildExportPackage(BiDashboard dashboard, 
                                   ResourceCollectorService.ResourceCollection resources,
                                   BiDashboardExportTask exportTask) {
        
        log.info("开始构建导出包，大屏: {}", dashboard.getName());
        
        try {
            // 1. 创建临时目录
            Path tempDir = createTempDirectory(exportTask.getTaskId());
            
            // 2. 更新任务状态
            updateTaskStatus(exportTask, BiDashboardExportTask.ExportStatus.PACKAGING, 
                           "开始打包", 0, resources.getTotalCount());
            
            // 3. 创建导出包结构
            createPackageStructure(tempDir, dashboard, resources, exportTask);
            
            // 4. 复制资源文件
            copyResourceFiles(tempDir, resources, exportTask);
            
            // 5. 生成配置文件
            generateConfigFiles(tempDir, dashboard, resources);
            
            // 6. 创建ZIP文件
            String zipFilePath = createZipFile(tempDir, dashboard, exportTask);
            
            // 7. 清理临时目录
            deleteDirectory(tempDir);
            
            // 8. 更新任务完成状态
            updateTaskCompleted(exportTask, zipFilePath);
            
            log.info("导出包构建完成: {}", zipFilePath);
            return zipFilePath;
            
        } catch (Exception e) {
            log.error("构建导出包失败", e);
            updateTaskFailed(exportTask, "构建导出包失败: " + e.getMessage());
            throw new RuntimeException("构建导出包失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建临时目录
     */
    private Path createTempDirectory(String taskId) throws IOException {
        Path exportDir = getExportDirectory();
        Path tempDir = exportDir.resolve("temp_" + taskId);
        
        if (Files.exists(tempDir)) {
            deleteDirectory(tempDir);
        }
        
        Files.createDirectories(tempDir);
        log.debug("创建临时目录: {}", tempDir);
        return tempDir;
    }
    
    /**
     * 获取导出目录
     */
    private Path getExportDirectory() throws IOException {
        Path exportDir = Paths.get(exportTempPath);
        if (!exportDir.isAbsolute()) {
            String userDir = System.getProperty("user.dir");
            exportDir = Paths.get(userDir, exportTempPath).toAbsolutePath().normalize();
        }
        
        if (!Files.exists(exportDir)) {
            Files.createDirectories(exportDir);
        }
        
        return exportDir;
    }
    
    /**
     * 创建导出包目录结构
     */
    private void createPackageStructure(Path tempDir, BiDashboard dashboard, 
                                      ResourceCollectorService.ResourceCollection resources,
                                      BiDashboardExportTask exportTask) throws IOException {
        
        // 创建资源目录
        Files.createDirectories(tempDir.resolve("resources/images"));
        Files.createDirectories(tempDir.resolve("resources/materials"));
        Files.createDirectories(tempDir.resolve("resources/videos"));
        Files.createDirectories(tempDir.resolve("resources/html_codes"));
        
        log.debug("创建导出包目录结构完成");
    }
    
    /**
     * 复制资源文件
     */
    private void copyResourceFiles(Path tempDir, ResourceCollectorService.ResourceCollection resources,
                                 BiDashboardExportTask exportTask) throws IOException {
        
        List<ResourceCollectorService.ResourceInfo> allResources = resources.getAllResources();
        int processed = 0;
        
        for (ResourceCollectorService.ResourceInfo resource : allResources) {
            if (resource.isExists()) {
                copyResourceFile(tempDir, resource);
            } else {
                log.warn("跳过不存在的资源文件: {}", resource.getLocalPath());
            }
            
            processed++;
            updateTaskStatus(exportTask, BiDashboardExportTask.ExportStatus.PACKAGING,
                           "复制资源文件", processed, allResources.size());
        }
        
        log.info("资源文件复制完成，成功: {} 个", processed);
    }
    
    /**
     * 复制单个资源文件
     */
    private void copyResourceFile(Path tempDir, ResourceCollectorService.ResourceInfo resource) throws IOException {
        Path sourcePath = Paths.get(resource.getLocalPath());
        Path targetPath = tempDir.resolve("resources/" + resource.getType() + "s/" + resource.getFilename());
        
        // 确保目标目录存在
        Files.createDirectories(targetPath.getParent());
        
        // 复制文件
        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
        
        log.debug("复制资源文件: {} -> {}", sourcePath, targetPath);
    }
    
    /**
     * 生成配置文件
     */
    private void generateConfigFiles(Path tempDir, BiDashboard dashboard, 
                                   ResourceCollectorService.ResourceCollection resources) throws IOException {
        
        // 1. 生成主配置文件 dashboard.json
        generateDashboardConfig(tempDir, dashboard, resources);
        
        // 2. 生成元数据文件 metadata.json
        generateMetadata(tempDir, dashboard, resources);
        
        // 3. 生成README文件
        generateReadme(tempDir, dashboard);
        
        log.debug("配置文件生成完成");
    }
    
    /**
     * 生成大屏配置文件
     */
    private void generateDashboardConfig(Path tempDir, BiDashboard dashboard,
                                       ResourceCollectorService.ResourceCollection resources) throws IOException {
        
        Map<String, Object> config = new HashMap<>();
        
        // 大屏基础信息
        Map<String, Object> dashboardInfo = new HashMap<>();
        dashboardInfo.put("id", dashboard.getId());
        dashboardInfo.put("name", dashboard.getName());
        dashboardInfo.put("description", dashboard.getDescription());
        dashboardInfo.put("canvasConfig", dashboard.getCanvasConfig());
        dashboardInfo.put("createdAt", dashboard.getCreatedAt());
        dashboardInfo.put("updatedAt", dashboard.getUpdatedAt());
        
        // 组件信息
        List<Map<String, Object>> widgets = new ArrayList<>();
        if (dashboard.getWidgets() != null) {
            for (var widget : dashboard.getWidgets()) {
                Map<String, Object> widgetInfo = new HashMap<>();
                widgetInfo.put("id", widget.getId());
                widgetInfo.put("widgetType", widget.getWidgetType());
                widgetInfo.put("setup", widget.getSetup());
                widgetInfo.put("data", widget.getData());
                widgetInfo.put("position", widget.getPosition());
                widgetInfo.put("options", widget.getOptions());
                widgetInfo.put("zIndex", widget.getZIndex());
                widgetInfo.put("refreshSeconds", widget.getRefreshSeconds());
                widgets.add(widgetInfo);
            }
        }
        dashboardInfo.put("widgets", widgets);
        
        config.put("dashboard", dashboardInfo);
        
        // 资源映射信息
        Map<String, Object> resourceMappings = new HashMap<>();
        generateResourceMappings(resources, resourceMappings);
        config.put("resourceMappings", resourceMappings);
        
        // 写入文件
        Path configFile = tempDir.resolve("dashboard.json");
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(configFile.toFile(), config);
        
        log.debug("生成大屏配置文件: {}", configFile);
    }
    
    /**
     * 生成资源映射
     */
    private void generateResourceMappings(ResourceCollectorService.ResourceCollection resources,
                                        Map<String, Object> resourceMappings) {
        
        // 图片映射
        Map<String, String> imageMappings = new HashMap<>();
        for (ResourceCollectorService.ResourceInfo resource : resources.getImages()) {
            if (resource.isExists()) {
                imageMappings.put(resource.getOriginalUrl(), "resources/images/" + resource.getFilename());
            }
        }
        resourceMappings.put("images", imageMappings);
        
        // 素材映射
        Map<String, String> materialMappings = new HashMap<>();
        for (ResourceCollectorService.ResourceInfo resource : resources.getMaterials()) {
            if (resource.isExists()) {
                materialMappings.put(resource.getOriginalUrl(), "resources/materials/" + resource.getFilename());
            }
        }
        resourceMappings.put("materials", materialMappings);
        
        // 视频映射
        Map<String, String> videoMappings = new HashMap<>();
        for (ResourceCollectorService.ResourceInfo resource : resources.getVideos()) {
            if (resource.isExists()) {
                videoMappings.put(resource.getOriginalUrl(), "resources/videos/" + resource.getFilename());
            }
        }
        resourceMappings.put("videos", videoMappings);
    }

    /**
     * 生成元数据文件
     */
    private void generateMetadata(Path tempDir, BiDashboard dashboard,
                                ResourceCollectorService.ResourceCollection resources) throws IOException {

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("exportVersion", "1.0");
        metadata.put("exportedAt", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        metadata.put("dashboardId", dashboard.getId());
        metadata.put("dashboardName", dashboard.getName());
        metadata.put("totalResources", resources.getTotalCount());
        metadata.put("totalSize", resources.getTotalSize());
        metadata.put("resourceTypes", resources.getResourceTypeCounts());

        // 系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("serverUrl", serverExternalUrl);
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        metadata.put("systemInfo", systemInfo);

        Path metadataFile = tempDir.resolve("metadata.json");
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(metadataFile.toFile(), metadata);

        log.debug("生成元数据文件: {}", metadataFile);
    }

    /**
     * 生成README文件
     */
    private void generateReadme(Path tempDir, BiDashboard dashboard) throws IOException {
        StringBuilder readme = new StringBuilder();
        readme.append("# BI大屏导出包\n\n");
        readme.append("## 基本信息\n");
        readme.append("- 大屏名称: ").append(dashboard.getName()).append("\n");
        readme.append("- 导出时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        readme.append("- 导出版本: 1.0\n\n");

        readme.append("## 文件结构\n");
        readme.append("```\n");
        readme.append("dashboard_export_[timestamp].zip\n");
        readme.append("├── dashboard.json          # 主配置文件\n");
        readme.append("├── resources/             # 资源文件夹\n");
        readme.append("│   ├── images/           # 图片资源\n");
        readme.append("│   ├── materials/        # 素材资源\n");
        readme.append("│   ├── videos/          # 视频资源\n");
        readme.append("│   └── html_codes/      # HTML代码片段\n");
        readme.append("├── metadata.json         # 导出元数据\n");
        readme.append("└── README.md            # 本说明文件\n");
        readme.append("```\n\n");

        readme.append("## 导入说明\n");
        readme.append("1. 使用系统的导入功能上传此ZIP文件\n");
        readme.append("2. 系统会自动解析配置并恢复资源文件\n");
        readme.append("3. 导入完成后会创建新的大屏副本\n\n");

        readme.append("## 注意事项\n");
        readme.append("- 请确保目标系统版本兼容\n");
        readme.append("- 资源文件路径会根据目标系统自动调整\n");
        readme.append("- 如有同名资源文件，可选择跳过或覆盖\n");

        Path readmeFile = tempDir.resolve("README.md");
        Files.write(readmeFile, readme.toString().getBytes("UTF-8"));

        log.debug("生成README文件: {}", readmeFile);
    }

    /**
     * 创建ZIP文件
     */
    private String createZipFile(Path tempDir, BiDashboard dashboard,
                               BiDashboardExportTask exportTask) throws IOException {

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String zipFileName = String.format("dashboard_export_%s_%s.zip",
                                         dashboard.getName().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_"),
                                         timestamp);

        Path exportDir = getExportDirectory();
        Path zipFilePath = exportDir.resolve(zipFileName);

        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFilePath.toFile()))) {
            zipDirectory(tempDir, tempDir, zos);
        }

        // 获取文件大小
        long fileSize = Files.size(zipFilePath);

        log.info("创建ZIP文件完成: {}, 大小: {} bytes", zipFilePath, fileSize);
        return zipFilePath.toString();
    }

    /**
     * 递归压缩目录
     */
    private void zipDirectory(Path sourceDir, Path basePath, ZipOutputStream zos) throws IOException {
        Files.walk(sourceDir)
             .filter(path -> !Files.isDirectory(path))
             .forEach(path -> {
                 try {
                     String relativePath = basePath.relativize(path).toString().replace("\\", "/");
                     ZipEntry zipEntry = new ZipEntry(relativePath);
                     zos.putNextEntry(zipEntry);

                     Files.copy(path, zos);
                     zos.closeEntry();

                 } catch (IOException e) {
                     log.error("压缩文件失败: {}", path, e);
                     throw new RuntimeException("压缩文件失败", e);
                 }
             });
    }

    /**
     * 删除目录
     */
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                 .sorted(Comparator.reverseOrder())
                 .map(Path::toFile)
                 .forEach(File::delete);
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(BiDashboardExportTask task, BiDashboardExportTask.ExportStatus status,
                                String message, int processed, int total) {
        task.setStatus(status);
        task.updateProgress(processed, total, message);
        exportTaskRepository.save(task);

        log.debug("更新任务状态: {} - {} ({}/{})", status, message, processed, total);
    }

    /**
     * 更新任务完成状态
     */
    private void updateTaskCompleted(BiDashboardExportTask task, String zipFilePath) {
        try {
            Path zipPath = Paths.get(zipFilePath);
            long fileSize = Files.size(zipPath);

            task.setStatus(BiDashboardExportTask.ExportStatus.COMPLETED);
            task.setProgress(100);
            task.setExportFilePath(zipFilePath);
            task.setExportFileName(zipPath.getFileName().toString());
            task.setExportFileSize(fileSize);
            task.setCompletedAt(LocalDateTime.now());

            exportTaskRepository.save(task);

        } catch (IOException e) {
            log.error("获取导出文件信息失败", e);
            updateTaskFailed(task, "获取导出文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新任务失败状态
     */
    private void updateTaskFailed(BiDashboardExportTask task, String errorMessage) {
        task.setStatus(BiDashboardExportTask.ExportStatus.FAILED);
        task.setErrorMessage(errorMessage);
        task.setCompletedAt(LocalDateTime.now());
        exportTaskRepository.save(task);

        log.error("导出任务失败: {}", errorMessage);
    }
}
