package com.example.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Service
@Slf4j
public class ImportParserService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 解析导入包
     */
    public ImportPackage parseImportPackage(String importFilePath) {
        log.info("开始解析导入包: {}", importFilePath);
        
        Path importFile = Paths.get(importFilePath);
        if (!Files.exists(importFile)) {
            throw new RuntimeException("导入文件不存在: " + importFilePath);
        }
        
        try {
            // 1. 创建临时解压目录
            Path tempDir = createTempDirectory();
            
            // 2. 解压ZIP文件
            extractZipFile(importFile, tempDir);
            
            // 3. 解析配置文件
            ImportPackage importPackage = parseConfigFiles(tempDir);
            importPackage.setTempDirectory(tempDir.toString());
            importPackage.setOriginalFilePath(importFilePath);
            
            log.info("导入包解析完成，大屏: {}, 资源数量: {}", 
                    importPackage.getDashboardName(), importPackage.getTotalResources());
            
            return importPackage;
            
        } catch (Exception e) {
            log.error("解析导入包失败", e);
            throw new RuntimeException("解析导入包失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建临时目录
     */
    private Path createTempDirectory() throws IOException {
        String tempDirPrefix = "import_" + System.currentTimeMillis() + "_";
        Path tempDir = Files.createTempDirectory(tempDirPrefix);
        log.debug("创建临时目录: {}", tempDir);
        return tempDir;
    }
    
    /**
     * 解压ZIP文件
     */
    private void extractZipFile(Path zipFile, Path targetDir) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile.toFile()))) {
            ZipEntry entry;
            
            while ((entry = zis.getNextEntry()) != null) {
                Path entryPath = targetDir.resolve(entry.getName());
                
                // 安全检查：防止目录遍历攻击
                if (!entryPath.normalize().startsWith(targetDir.normalize())) {
                    throw new IOException("不安全的ZIP条目: " + entry.getName());
                }
                
                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                } else {
                    // 确保父目录存在
                    Files.createDirectories(entryPath.getParent());
                    
                    // 提取文件
                    try (OutputStream os = Files.newOutputStream(entryPath)) {
                        byte[] buffer = new byte[8192];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            os.write(buffer, 0, length);
                        }
                    }
                }
                
                zis.closeEntry();
            }
        }
        
        log.debug("ZIP文件解压完成: {}", zipFile);
    }
    
    /**
     * 解析配置文件
     */
    private ImportPackage parseConfigFiles(Path tempDir) throws IOException {
        ImportPackage importPackage = new ImportPackage();
        
        // 1. 解析主配置文件
        parseDashboardConfig(tempDir, importPackage);
        
        // 2. 解析元数据文件
        parseMetadata(tempDir, importPackage);
        
        // 3. 扫描资源文件
        scanResourceFiles(tempDir, importPackage);
        
        return importPackage;
    }
    
    /**
     * 解析大屏配置文件
     */
    private void parseDashboardConfig(Path tempDir, ImportPackage importPackage) throws IOException {
        Path configFile = tempDir.resolve("dashboard.json");
        if (!Files.exists(configFile)) {
            throw new RuntimeException("缺少主配置文件: dashboard.json");
        }
        
        JsonNode configNode = objectMapper.readTree(configFile.toFile());
        
        // 解析大屏信息
        JsonNode dashboardNode = configNode.get("dashboard");
        if (dashboardNode == null) {
            throw new RuntimeException("配置文件格式错误：缺少dashboard节点");
        }
        
        DashboardConfig dashboardConfig = new DashboardConfig();
        dashboardConfig.setOriginalId(dashboardNode.get("id").asLong());
        dashboardConfig.setName(dashboardNode.get("name").asText());
        dashboardConfig.setDescription(dashboardNode.get("description").asText());
        dashboardConfig.setCanvasConfig(dashboardNode.get("canvasConfig").toString());
        
        // 解析组件信息
        JsonNode widgetsNode = dashboardNode.get("widgets");
        if (widgetsNode != null && widgetsNode.isArray()) {
            List<WidgetConfig> widgets = new ArrayList<>();
            for (JsonNode widgetNode : widgetsNode) {
                WidgetConfig widget = new WidgetConfig();
                widget.setOriginalId(widgetNode.get("id").asLong());
                widget.setWidgetType(widgetNode.get("widgetType").asText());
                widget.setSetup(widgetNode.get("setup").toString());
                widget.setData(widgetNode.get("data").toString());
                widget.setPosition(widgetNode.get("position").toString());
                widget.setOptions(widgetNode.get("options").toString());
                widget.setZIndex(widgetNode.get("zIndex").asInt());
                widget.setRefreshSeconds(widgetNode.get("refreshSeconds").asInt());
                widgets.add(widget);
            }
            dashboardConfig.setWidgets(widgets);
        }
        
        importPackage.setDashboardConfig(dashboardConfig);
        
        // 解析资源映射
        JsonNode resourceMappingsNode = configNode.get("resourceMappings");
        if (resourceMappingsNode != null) {
            parseResourceMappings(resourceMappingsNode, importPackage);
        }
        
        log.debug("解析大屏配置完成: {}", dashboardConfig.getName());
    }
    
    /**
     * 解析资源映射
     */
    private void parseResourceMappings(JsonNode resourceMappingsNode, ImportPackage importPackage) {
        Map<String, String> resourceMappings = new HashMap<>();
        
        // 图片映射
        JsonNode imagesNode = resourceMappingsNode.get("images");
        if (imagesNode != null) {
            imagesNode.fields().forEachRemaining(entry -> {
                resourceMappings.put(entry.getKey(), entry.getValue().asText());
            });
        }
        
        // 素材映射
        JsonNode materialsNode = resourceMappingsNode.get("materials");
        if (materialsNode != null) {
            materialsNode.fields().forEachRemaining(entry -> {
                resourceMappings.put(entry.getKey(), entry.getValue().asText());
            });
        }
        
        // 视频映射
        JsonNode videosNode = resourceMappingsNode.get("videos");
        if (videosNode != null) {
            videosNode.fields().forEachRemaining(entry -> {
                resourceMappings.put(entry.getKey(), entry.getValue().asText());
            });
        }
        
        importPackage.setResourceMappings(resourceMappings);
        log.debug("解析资源映射完成，映射数量: {}", resourceMappings.size());
    }
    
    /**
     * 解析元数据文件
     */
    private void parseMetadata(Path tempDir, ImportPackage importPackage) throws IOException {
        Path metadataFile = tempDir.resolve("metadata.json");
        if (!Files.exists(metadataFile)) {
            log.warn("元数据文件不存在，跳过解析");
            return;
        }
        
        JsonNode metadataNode = objectMapper.readTree(metadataFile.toFile());
        
        PackageMetadata metadata = new PackageMetadata();
        metadata.setExportVersion(metadataNode.get("exportVersion").asText());
        metadata.setExportedAt(metadataNode.get("exportedAt").asText());
        metadata.setOriginalDashboardId(metadataNode.get("dashboardId").asLong());
        metadata.setDashboardName(metadataNode.get("dashboardName").asText());
        metadata.setTotalResources(metadataNode.get("totalResources").asInt());
        metadata.setTotalSize(metadataNode.get("totalSize").asLong());
        
        // 解析资源类型统计
        JsonNode resourceTypesNode = metadataNode.get("resourceTypes");
        if (resourceTypesNode != null) {
            Map<String, Integer> resourceTypes = new HashMap<>();
            resourceTypesNode.fields().forEachRemaining(entry -> {
                resourceTypes.put(entry.getKey(), entry.getValue().asInt());
            });
            metadata.setResourceTypes(resourceTypes);
        }
        
        // 解析系统信息
        JsonNode systemInfoNode = metadataNode.get("systemInfo");
        if (systemInfoNode != null) {
            Map<String, String> systemInfo = new HashMap<>();
            systemInfoNode.fields().forEachRemaining(entry -> {
                systemInfo.put(entry.getKey(), entry.getValue().asText());
            });
            metadata.setSystemInfo(systemInfo);
        }
        
        importPackage.setMetadata(metadata);
        log.debug("解析元数据完成");
    }
    
    /**
     * 扫描资源文件
     */
    private void scanResourceFiles(Path tempDir, ImportPackage importPackage) throws IOException {
        Path resourcesDir = tempDir.resolve("resources");
        if (!Files.exists(resourcesDir)) {
            log.warn("资源目录不存在，跳过扫描");
            return;
        }
        
        List<ResourceFileInfo> resourceFiles = new ArrayList<>();
        
        // 扫描各类型资源目录
        scanResourceDirectory(resourcesDir.resolve("images"), "image", resourceFiles);
        scanResourceDirectory(resourcesDir.resolve("materials"), "material", resourceFiles);
        scanResourceDirectory(resourcesDir.resolve("videos"), "video", resourceFiles);
        scanResourceDirectory(resourcesDir.resolve("html_codes"), "html_code", resourceFiles);
        
        importPackage.setResourceFiles(resourceFiles);
        importPackage.setTotalResources(resourceFiles.size());
        
        log.debug("扫描资源文件完成，总数: {}", resourceFiles.size());
    }
    
    /**
     * 扫描资源目录
     */
    private void scanResourceDirectory(Path resourceDir, String resourceType, 
                                     List<ResourceFileInfo> resourceFiles) throws IOException {
        if (!Files.exists(resourceDir)) {
            return;
        }
        
        Files.walk(resourceDir, 1)
             .filter(Files::isRegularFile)
             .forEach(filePath -> {
                 try {
                     ResourceFileInfo fileInfo = new ResourceFileInfo();
                     fileInfo.setType(resourceType);
                     fileInfo.setFileName(filePath.getFileName().toString());
                     fileInfo.setFilePath(filePath.toString());
                     fileInfo.setFileSize(Files.size(filePath));
                     fileInfo.setRelativePath("resources/" + resourceType + "s/" + fileInfo.getFileName());
                     
                     resourceFiles.add(fileInfo);
                     
                 } catch (IOException e) {
                     log.warn("获取文件信息失败: {}", filePath, e);
                 }
             });
    }
    
    /**
     * 清理临时目录
     */
    public void cleanupTempDirectory(String tempDirPath) {
        if (tempDirPath == null || tempDirPath.trim().isEmpty()) {
            return;
        }
        
        try {
            Path tempDir = Paths.get(tempDirPath);
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                     .sorted(Comparator.reverseOrder())
                     .map(Path::toFile)
                     .forEach(File::delete);
                
                log.debug("清理临时目录: {}", tempDir);
            }
        } catch (IOException e) {
            log.warn("清理临时目录失败: {}", tempDirPath, e);
        }
    }

    /**
     * 导入包信息类
     */
    public static class ImportPackage {
        private String tempDirectory;
        private String originalFilePath;
        private DashboardConfig dashboardConfig;
        private PackageMetadata metadata;
        private Map<String, String> resourceMappings = new HashMap<>();
        private List<ResourceFileInfo> resourceFiles = new ArrayList<>();
        private int totalResources;

        // Getters and Setters
        public String getTempDirectory() { return tempDirectory; }
        public void setTempDirectory(String tempDirectory) { this.tempDirectory = tempDirectory; }

        public String getOriginalFilePath() { return originalFilePath; }
        public void setOriginalFilePath(String originalFilePath) { this.originalFilePath = originalFilePath; }

        public DashboardConfig getDashboardConfig() { return dashboardConfig; }
        public void setDashboardConfig(DashboardConfig dashboardConfig) { this.dashboardConfig = dashboardConfig; }

        public PackageMetadata getMetadata() { return metadata; }
        public void setMetadata(PackageMetadata metadata) { this.metadata = metadata; }

        public Map<String, String> getResourceMappings() { return resourceMappings; }
        public void setResourceMappings(Map<String, String> resourceMappings) { this.resourceMappings = resourceMappings; }

        public List<ResourceFileInfo> getResourceFiles() { return resourceFiles; }
        public void setResourceFiles(List<ResourceFileInfo> resourceFiles) { this.resourceFiles = resourceFiles; }

        public int getTotalResources() { return totalResources; }
        public void setTotalResources(int totalResources) { this.totalResources = totalResources; }

        public String getDashboardName() {
            return dashboardConfig != null ? dashboardConfig.getName() : "未知";
        }
    }

    /**
     * 大屏配置类
     */
    public static class DashboardConfig {
        private Long originalId;
        private String name;
        private String description;
        private String canvasConfig;
        private List<WidgetConfig> widgets = new ArrayList<>();

        // Getters and Setters
        public Long getOriginalId() { return originalId; }
        public void setOriginalId(Long originalId) { this.originalId = originalId; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getCanvasConfig() { return canvasConfig; }
        public void setCanvasConfig(String canvasConfig) { this.canvasConfig = canvasConfig; }

        public List<WidgetConfig> getWidgets() { return widgets; }
        public void setWidgets(List<WidgetConfig> widgets) { this.widgets = widgets; }
    }

    /**
     * 组件配置类
     */
    public static class WidgetConfig {
        private Long originalId;
        private String widgetType;
        private String setup;
        private String data;
        private String position;
        private String options;
        private Integer zIndex;
        private Integer refreshSeconds;

        // Getters and Setters
        public Long getOriginalId() { return originalId; }
        public void setOriginalId(Long originalId) { this.originalId = originalId; }

        public String getWidgetType() { return widgetType; }
        public void setWidgetType(String widgetType) { this.widgetType = widgetType; }

        public String getSetup() { return setup; }
        public void setSetup(String setup) { this.setup = setup; }

        public String getData() { return data; }
        public void setData(String data) { this.data = data; }

        public String getPosition() { return position; }
        public void setPosition(String position) { this.position = position; }

        public String getOptions() { return options; }
        public void setOptions(String options) { this.options = options; }

        public Integer getZIndex() { return zIndex; }
        public void setZIndex(Integer zIndex) { this.zIndex = zIndex; }

        public Integer getRefreshSeconds() { return refreshSeconds; }
        public void setRefreshSeconds(Integer refreshSeconds) { this.refreshSeconds = refreshSeconds; }
    }

    /**
     * 包元数据类
     */
    public static class PackageMetadata {
        private String exportVersion;
        private String exportedAt;
        private Long originalDashboardId;
        private String dashboardName;
        private Integer totalResources;
        private Long totalSize;
        private Map<String, Integer> resourceTypes = new HashMap<>();
        private Map<String, String> systemInfo = new HashMap<>();

        // Getters and Setters
        public String getExportVersion() { return exportVersion; }
        public void setExportVersion(String exportVersion) { this.exportVersion = exportVersion; }

        public String getExportedAt() { return exportedAt; }
        public void setExportedAt(String exportedAt) { this.exportedAt = exportedAt; }

        public Long getOriginalDashboardId() { return originalDashboardId; }
        public void setOriginalDashboardId(Long originalDashboardId) { this.originalDashboardId = originalDashboardId; }

        public String getDashboardName() { return dashboardName; }
        public void setDashboardName(String dashboardName) { this.dashboardName = dashboardName; }

        public Integer getTotalResources() { return totalResources; }
        public void setTotalResources(Integer totalResources) { this.totalResources = totalResources; }

        public Long getTotalSize() { return totalSize; }
        public void setTotalSize(Long totalSize) { this.totalSize = totalSize; }

        public Map<String, Integer> getResourceTypes() { return resourceTypes; }
        public void setResourceTypes(Map<String, Integer> resourceTypes) { this.resourceTypes = resourceTypes; }

        public Map<String, String> getSystemInfo() { return systemInfo; }
        public void setSystemInfo(Map<String, String> systemInfo) { this.systemInfo = systemInfo; }
    }

    /**
     * 资源文件信息类
     */
    public static class ResourceFileInfo {
        private String type;
        private String fileName;
        private String filePath;
        private String relativePath;
        private Long fileSize;

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public String getRelativePath() { return relativePath; }
        public void setRelativePath(String relativePath) { this.relativePath = relativePath; }

        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    }
}
