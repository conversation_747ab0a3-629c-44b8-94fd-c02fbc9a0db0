package com.example.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportResult {
    
    private boolean success;
    
    private String message;
    
    private Long newDashboardId;
    
    private String newDashboardName;
    
    private ImportSummary importSummary;
    
    private List<String> warnings;
    
    private List<String> errors;
    
    private LocalDateTime importedAt;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportSummary {
        private int totalResources;
        private int successfulResources;
        private int skippedResources;
        private int failedResources;
        private long totalSize;
        private Map<String, Integer> resourceTypes; // 各类型资源数量统计
        private List<ResourceImportDetail> resourceDetails;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceImportDetail {
        private String resourceType;
        private String originalPath;
        private String newPath;
        private String status; // SUCCESS, SKIPPED, FAILED
        private String message;
        private long fileSize;
    }
    
    // 创建成功结果
    public static ImportResult success(Long dashboardId, String dashboardName, ImportSummary summary) {
        ImportResult result = new ImportResult();
        result.setSuccess(true);
        result.setMessage("导入成功");
        result.setNewDashboardId(dashboardId);
        result.setNewDashboardName(dashboardName);
        result.setImportSummary(summary);
        result.setImportedAt(LocalDateTime.now());
        return result;
    }
    
    // 创建失败结果
    public static ImportResult failure(String message, List<String> errors) {
        ImportResult result = new ImportResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setErrors(errors);
        result.setImportedAt(LocalDateTime.now());
        return result;
    }
}
