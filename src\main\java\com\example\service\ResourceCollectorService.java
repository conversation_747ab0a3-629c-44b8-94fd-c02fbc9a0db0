package com.example.service;

import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class ResourceCollectorService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${upload.image.path}")
    private String imageUploadPath;
    
    @Value("${upload.material.path}")
    private String materialUploadPath;
    
    @Value("${upload.video.path}")
    private String videoUploadPath;
    
    @Value("${server.external-url:localhost:8080}")
    private String serverExternalUrl;
    
    // URL匹配模式
    private static final Pattern IMAGE_URL_PATTERN = Pattern.compile("http://[^/]+/images/([^\\s\"']+)");
    private static final Pattern MATERIAL_URL_PATTERN = Pattern.compile("http://[^/]+/materials/([^\\s\"']+)");
    private static final Pattern VIDEO_URL_PATTERN = Pattern.compile("http://[^/]+/videos/([^\\s\"']+)");
    
    /**
     * 收集大屏中引用的所有资源
     */
    public ResourceCollection collectResources(BiDashboard dashboard) {
        log.info("开始收集大屏资源，大屏ID: {}", dashboard.getId());
        
        ResourceCollection collection = new ResourceCollection();
        
        try {
            // 1. 收集画布背景资源
            collectCanvasResources(dashboard.getCanvasConfig(), collection);
            
            // 2. 收集组件资源
            if (dashboard.getWidgets() != null) {
                for (BiWidget widget : dashboard.getWidgets()) {
                    collectWidgetResources(widget, collection);
                }
            }
            
            // 3. 验证资源文件存在性
            validateResourceFiles(collection);
            
            log.info("资源收集完成，总计: {} 个文件", collection.getTotalCount());
            
        } catch (Exception e) {
            log.error("收集大屏资源失败", e);
            throw new RuntimeException("收集资源失败: " + e.getMessage());
        }
        
        return collection;
    }
    
    /**
     * 收集画布配置中的资源
     */
    private void collectCanvasResources(String canvasConfig, ResourceCollection collection) {
        if (canvasConfig == null || canvasConfig.trim().isEmpty()) {
            return;
        }
        
        try {
            JsonNode canvasNode = objectMapper.readTree(canvasConfig);
            
            // 检查背景图片
            JsonNode backgroundImageNode = canvasNode.get("backgroundImage");
            if (backgroundImageNode != null && !backgroundImageNode.isNull()) {
                String backgroundImageUrl = backgroundImageNode.asText();
                if (backgroundImageUrl != null && !backgroundImageUrl.trim().isEmpty()) {
                    addResourceFromUrl(backgroundImageUrl, collection, "canvas_background");
                }
            }
            
        } catch (Exception e) {
            log.warn("解析画布配置失败: {}", e.getMessage());
        }
    }
    
    /**
     * 收集组件中的资源
     */
    private void collectWidgetResources(BiWidget widget, ResourceCollection collection) {
        try {
            // 收集样式配置中的资源
            collectFromJsonConfig(widget.getSetup(), collection, "widget_" + widget.getId() + "_setup");
            
            // 收集数据配置中的资源
            collectFromJsonConfig(widget.getData(), collection, "widget_" + widget.getId() + "_data");
            
            // 收集位置配置中的资源（如果有背景图等）
            collectFromJsonConfig(widget.getPosition(), collection, "widget_" + widget.getId() + "_position");
            
            // 收集选项配置中的资源
            collectFromJsonConfig(widget.getOptions(), collection, "widget_" + widget.getId() + "_options");
            
            // 特殊处理HTML代码组件
            if ("html-code".equals(widget.getWidgetType())) {
                collectHtmlCodeResources(widget, collection);
            }
            
        } catch (Exception e) {
            log.warn("收集组件 {} 资源失败: {}", widget.getId(), e.getMessage());
        }
    }
    
    /**
     * 从JSON配置中收集资源
     */
    private void collectFromJsonConfig(String jsonConfig, ResourceCollection collection, String context) {
        if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
            return;
        }
        
        try {
            // 直接在JSON字符串中搜索URL模式
            addResourcesFromText(jsonConfig, collection, context);
            
        } catch (Exception e) {
            log.warn("从JSON配置收集资源失败 [{}]: {}", context, e.getMessage());
        }
    }
    
    /**
     * 收集HTML代码组件中的资源
     */
    private void collectHtmlCodeResources(BiWidget widget, ResourceCollection collection) {
        try {
            String setupJson = widget.getSetup();
            if (setupJson != null && !setupJson.trim().isEmpty()) {
                JsonNode setupNode = objectMapper.readTree(setupJson);
                JsonNode htmlContentNode = setupNode.get("htmlContent");
                
                if (htmlContentNode != null && !htmlContentNode.isNull()) {
                    String htmlContent = htmlContentNode.asText();
                    addResourcesFromText(htmlContent, collection, "html_code_" + widget.getId());
                }
            }
        } catch (Exception e) {
            log.warn("收集HTML代码组件资源失败: {}", e.getMessage());
        }
    }
    
    /**
     * 从文本中提取并添加资源URL
     */
    private void addResourcesFromText(String text, ResourceCollection collection, String context) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        // 匹配图片URL
        Matcher imageMatcher = IMAGE_URL_PATTERN.matcher(text);
        while (imageMatcher.find()) {
            String filename = imageMatcher.group(1);
            String fullUrl = imageMatcher.group(0);
            addImageResource(filename, fullUrl, collection, context);
        }
        
        // 匹配素材URL
        Matcher materialMatcher = MATERIAL_URL_PATTERN.matcher(text);
        while (materialMatcher.find()) {
            String filename = materialMatcher.group(1);
            String fullUrl = materialMatcher.group(0);
            addMaterialResource(filename, fullUrl, collection, context);
        }
        
        // 匹配视频URL
        Matcher videoMatcher = VIDEO_URL_PATTERN.matcher(text);
        while (videoMatcher.find()) {
            String filename = videoMatcher.group(1);
            String fullUrl = videoMatcher.group(0);
            addVideoResource(filename, fullUrl, collection, context);
        }
    }
    
    /**
     * 从URL添加资源
     */
    private void addResourceFromUrl(String url, ResourceCollection collection, String context) {
        if (url == null || url.trim().isEmpty()) {
            return;
        }
        
        if (url.contains("/images/")) {
            Matcher matcher = IMAGE_URL_PATTERN.matcher(url);
            if (matcher.find()) {
                String filename = matcher.group(1);
                addImageResource(filename, url, collection, context);
            }
        } else if (url.contains("/materials/")) {
            Matcher matcher = MATERIAL_URL_PATTERN.matcher(url);
            if (matcher.find()) {
                String filename = matcher.group(1);
                addMaterialResource(filename, url, collection, context);
            }
        } else if (url.contains("/videos/")) {
            Matcher matcher = VIDEO_URL_PATTERN.matcher(url);
            if (matcher.find()) {
                String filename = matcher.group(1);
                addVideoResource(filename, url, collection, context);
            }
        }
    }
    
    /**
     * 添加图片资源
     */
    private void addImageResource(String filename, String url, ResourceCollection collection, String context) {
        Path imagePath = getAbsolutePath(imageUploadPath).resolve(filename);
        ResourceInfo resource = new ResourceInfo("image", filename, url, imagePath.toString(), context);
        collection.addImage(resource);
    }
    
    /**
     * 添加素材资源
     */
    private void addMaterialResource(String filename, String url, ResourceCollection collection, String context) {
        Path materialPath = getAbsolutePath(materialUploadPath).resolve(filename);
        ResourceInfo resource = new ResourceInfo("material", filename, url, materialPath.toString(), context);
        collection.addMaterial(resource);
    }
    
    /**
     * 添加视频资源
     */
    private void addVideoResource(String filename, String url, ResourceCollection collection, String context) {
        Path videoPath = getAbsolutePath(videoUploadPath).resolve(filename);
        ResourceInfo resource = new ResourceInfo("video", filename, url, videoPath.toString(), context);
        collection.addVideo(resource);
    }
    
    /**
     * 获取绝对路径
     */
    private Path getAbsolutePath(String configPath) {
        Path path = Paths.get(configPath);
        if (path.isAbsolute()) {
            return path.normalize();
        } else {
            String userDir = System.getProperty("user.dir");
            return Paths.get(userDir, configPath).toAbsolutePath().normalize();
        }
    }
    
    /**
     * 验证资源文件是否存在
     */
    private void validateResourceFiles(ResourceCollection collection) {
        List<ResourceInfo> allResources = new ArrayList<>();
        allResources.addAll(collection.getImages());
        allResources.addAll(collection.getMaterials());
        allResources.addAll(collection.getVideos());
        
        for (ResourceInfo resource : allResources) {
            File file = new File(resource.getLocalPath());
            if (file.exists() && file.isFile()) {
                resource.setExists(true);
                resource.setFileSize(file.length());
            } else {
                resource.setExists(false);
                log.warn("资源文件不存在: {} (来源: {})", resource.getLocalPath(), resource.getContext());
            }
        }
    }

    /**
     * 资源信息类
     */
    public static class ResourceInfo {
        private String type;
        private String filename;
        private String originalUrl;
        private String localPath;
        private String context;
        private boolean exists;
        private long fileSize;

        public ResourceInfo(String type, String filename, String originalUrl, String localPath, String context) {
            this.type = type;
            this.filename = filename;
            this.originalUrl = originalUrl;
            this.localPath = localPath;
            this.context = context;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public String getFilename() { return filename; }
        public void setFilename(String filename) { this.filename = filename; }

        public String getOriginalUrl() { return originalUrl; }
        public void setOriginalUrl(String originalUrl) { this.originalUrl = originalUrl; }

        public String getLocalPath() { return localPath; }
        public void setLocalPath(String localPath) { this.localPath = localPath; }

        public String getContext() { return context; }
        public void setContext(String context) { this.context = context; }

        public boolean isExists() { return exists; }
        public void setExists(boolean exists) { this.exists = exists; }

        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ResourceInfo that = (ResourceInfo) o;
            return Objects.equals(filename, that.filename) && Objects.equals(type, that.type);
        }

        @Override
        public int hashCode() {
            return Objects.hash(filename, type);
        }
    }

    /**
     * 资源集合类
     */
    public static class ResourceCollection {
        private Set<ResourceInfo> images = new LinkedHashSet<>();
        private Set<ResourceInfo> materials = new LinkedHashSet<>();
        private Set<ResourceInfo> videos = new LinkedHashSet<>();
        private Set<ResourceInfo> htmlCodes = new LinkedHashSet<>();

        public void addImage(ResourceInfo resource) {
            images.add(resource);
        }

        public void addMaterial(ResourceInfo resource) {
            materials.add(resource);
        }

        public void addVideo(ResourceInfo resource) {
            videos.add(resource);
        }

        public void addHtmlCode(ResourceInfo resource) {
            htmlCodes.add(resource);
        }

        public Set<ResourceInfo> getImages() { return images; }
        public Set<ResourceInfo> getMaterials() { return materials; }
        public Set<ResourceInfo> getVideos() { return videos; }
        public Set<ResourceInfo> getHtmlCodes() { return htmlCodes; }

        public int getTotalCount() {
            return images.size() + materials.size() + videos.size() + htmlCodes.size();
        }

        public long getTotalSize() {
            long total = 0;
            for (ResourceInfo resource : images) {
                if (resource.isExists()) total += resource.getFileSize();
            }
            for (ResourceInfo resource : materials) {
                if (resource.isExists()) total += resource.getFileSize();
            }
            for (ResourceInfo resource : videos) {
                if (resource.isExists()) total += resource.getFileSize();
            }
            for (ResourceInfo resource : htmlCodes) {
                if (resource.isExists()) total += resource.getFileSize();
            }
            return total;
        }

        public List<ResourceInfo> getAllResources() {
            List<ResourceInfo> all = new ArrayList<>();
            all.addAll(images);
            all.addAll(materials);
            all.addAll(videos);
            all.addAll(htmlCodes);
            return all;
        }

        public Map<String, Integer> getResourceTypeCounts() {
            Map<String, Integer> counts = new HashMap<>();
            counts.put("images", images.size());
            counts.put("materials", materials.size());
            counts.put("videos", videos.size());
            counts.put("htmlCodes", htmlCodes.size());
            return counts;
        }
    }
}
