package com.example.service;

import com.example.dto.ImportRequest;
import com.example.dto.ImportResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class BiDashboardImportService {
    
    @Autowired
    private ImportParserService importParserService;
    
    @Autowired
    private ImportRestoreService importRestoreService;
    
    /**
     * 导入大屏（从上传文件）
     */
    @Transactional
    public ImportResult importFromFile(MultipartFile file, ImportRequest request) {
        log.info("开始导入大屏，文件: {}", file.getOriginalFilename());
        
        ImportParserService.ImportPackage importPackage = null;
        
        try {
            // 1. 验证文件
            validateImportFile(file);
            
            // 2. 保存上传文件到临时位置
            String tempFilePath = saveUploadedFile(file);
            request.setImportFilePath(tempFilePath);
            
            // 3. 解析导入包
            importPackage = importParserService.parseImportPackage(tempFilePath);
            
            // 4. 恢复导入包
            ImportResult result = importRestoreService.restoreImportPackage(importPackage, request);
            
            // 5. 清理临时文件
            cleanupTempFile(tempFilePath);
            
            log.info("大屏导入完成，新大屏ID: {}", result.getNewDashboardId());
            return result;
            
        } catch (Exception e) {
            log.error("导入大屏失败", e);
            return ImportResult.failure("导入失败: " + e.getMessage(), Arrays.asList(e.getMessage()));
            
        } finally {
            // 清理临时目录
            if (importPackage != null) {
                importParserService.cleanupTempDirectory(importPackage.getTempDirectory());
            }
        }
    }
    
    /**
     * 导入大屏（从文件路径）
     */
    @Transactional
    public ImportResult importFromPath(ImportRequest request) {
        log.info("开始导入大屏，文件路径: {}", request.getImportFilePath());
        
        ImportParserService.ImportPackage importPackage = null;
        
        try {
            // 1. 验证文件路径
            validateImportFilePath(request.getImportFilePath());
            
            // 2. 解析导入包
            importPackage = importParserService.parseImportPackage(request.getImportFilePath());
            
            // 3. 恢复导入包
            ImportResult result = importRestoreService.restoreImportPackage(importPackage, request);
            
            log.info("大屏导入完成，新大屏ID: {}", result.getNewDashboardId());
            return result;
            
        } catch (Exception e) {
            log.error("导入大屏失败", e);
            return ImportResult.failure("导入失败: " + e.getMessage(), Arrays.asList(e.getMessage()));
            
        } finally {
            // 清理临时目录
            if (importPackage != null) {
                importParserService.cleanupTempDirectory(importPackage.getTempDirectory());
            }
        }
    }
    
    /**
     * 预览导入包信息（不实际导入）
     */
    public ImportPreview previewImportPackage(MultipartFile file) {
        log.info("预览导入包，文件: {}", file.getOriginalFilename());
        
        ImportParserService.ImportPackage importPackage = null;
        String tempFilePath = null;
        
        try {
            // 1. 验证文件
            validateImportFile(file);
            
            // 2. 保存上传文件到临时位置
            tempFilePath = saveUploadedFile(file);
            
            // 3. 解析导入包
            importPackage = importParserService.parseImportPackage(tempFilePath);
            
            // 4. 创建预览信息
            ImportPreview preview = createImportPreview(importPackage);
            
            log.info("导入包预览完成: {}", importPackage.getDashboardName());
            return preview;
            
        } catch (Exception e) {
            log.error("预览导入包失败", e);
            throw new RuntimeException("预览导入包失败: " + e.getMessage());
            
        } finally {
            // 清理临时文件和目录
            if (tempFilePath != null) {
                cleanupTempFile(tempFilePath);
            }
            if (importPackage != null) {
                importParserService.cleanupTempDirectory(importPackage.getTempDirectory());
            }
        }
    }
    
    /**
     * 预览导入包信息（从文件路径）
     */
    public ImportPreview previewImportPackageFromPath(String filePath) {
        log.info("预览导入包，文件路径: {}", filePath);
        
        ImportParserService.ImportPackage importPackage = null;
        
        try {
            // 1. 验证文件路径
            validateImportFilePath(filePath);
            
            // 2. 解析导入包
            importPackage = importParserService.parseImportPackage(filePath);
            
            // 3. 创建预览信息
            ImportPreview preview = createImportPreview(importPackage);
            
            log.info("导入包预览完成: {}", importPackage.getDashboardName());
            return preview;
            
        } catch (Exception e) {
            log.error("预览导入包失败", e);
            throw new RuntimeException("预览导入包失败: " + e.getMessage());
            
        } finally {
            // 清理临时目录
            if (importPackage != null) {
                importParserService.cleanupTempDirectory(importPackage.getTempDirectory());
            }
        }
    }
    
    /**
     * 验证导入文件
     */
    private void validateImportFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("导入文件不能为空");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".zip")) {
            throw new RuntimeException("导入文件必须是ZIP格式");
        }
        
        // 检查文件大小（限制为100MB）
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            throw new RuntimeException("导入文件大小不能超过100MB");
        }
        
        log.debug("导入文件验证通过: {}, 大小: {} bytes", originalFilename, file.getSize());
    }
    
    /**
     * 验证导入文件路径
     */
    private void validateImportFilePath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new RuntimeException("导入文件路径不能为空");
        }
        
        Path file = Paths.get(filePath);
        if (!Files.exists(file)) {
            throw new RuntimeException("导入文件不存在: " + filePath);
        }
        
        if (!Files.isRegularFile(file)) {
            throw new RuntimeException("导入路径不是文件: " + filePath);
        }
        
        if (!filePath.toLowerCase().endsWith(".zip")) {
            throw new RuntimeException("导入文件必须是ZIP格式");
        }
        
        log.debug("导入文件路径验证通过: {}", filePath);
    }
    
    /**
     * 保存上传文件到临时位置
     */
    private String saveUploadedFile(MultipartFile file) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("import_upload_");
        
        // 生成临时文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String tempFileName = "import_" + timestamp + ".zip";
        Path tempFile = tempDir.resolve(tempFileName);
        
        // 保存文件
        Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        
        log.debug("上传文件已保存到临时位置: {}", tempFile);
        return tempFile.toString();
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFile(String tempFilePath) {
        try {
            Path tempFile = Paths.get(tempFilePath);
            if (Files.exists(tempFile)) {
                Files.delete(tempFile);
                
                // 尝试删除父目录（如果为空）
                Path parentDir = tempFile.getParent();
                if (Files.exists(parentDir) && Files.list(parentDir).count() == 0) {
                    Files.delete(parentDir);
                }
                
                log.debug("清理临时文件: {}", tempFile);
            }
        } catch (IOException e) {
            log.warn("清理临时文件失败: {}", tempFilePath, e);
        }
    }
    
    /**
     * 创建导入预览信息
     */
    private ImportPreview createImportPreview(ImportParserService.ImportPackage importPackage) {
        ImportPreview preview = new ImportPreview();
        
        // 基本信息
        ImportParserService.DashboardConfig dashboardConfig = importPackage.getDashboardConfig();
        preview.setDashboardName(dashboardConfig.getName());
        preview.setDescription(dashboardConfig.getDescription());
        preview.setWidgetCount(dashboardConfig.getWidgets().size());
        
        // 资源信息
        preview.setTotalResources(importPackage.getTotalResources());
        preview.setResourceFiles(importPackage.getResourceFiles());
        
        // 元数据信息
        if (importPackage.getMetadata() != null) {
            ImportParserService.PackageMetadata metadata = importPackage.getMetadata();
            preview.setExportVersion(metadata.getExportVersion());
            preview.setExportedAt(metadata.getExportedAt());
            preview.setOriginalDashboardId(metadata.getOriginalDashboardId());
            preview.setTotalSize(metadata.getTotalSize());
            preview.setResourceTypes(metadata.getResourceTypes());
            preview.setSystemInfo(metadata.getSystemInfo());
        }
        
        return preview;
    }

    /**
     * 导入预览信息类
     */
    public static class ImportPreview {
        private String dashboardName;
        private String description;
        private Integer widgetCount;
        private Integer totalResources;
        private Long totalSize;
        private String exportVersion;
        private String exportedAt;
        private Long originalDashboardId;
        private List<ImportParserService.ResourceFileInfo> resourceFiles;
        private java.util.Map<String, Integer> resourceTypes;
        private java.util.Map<String, String> systemInfo;

        // Getters and Setters
        public String getDashboardName() { return dashboardName; }
        public void setDashboardName(String dashboardName) { this.dashboardName = dashboardName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public Integer getWidgetCount() { return widgetCount; }
        public void setWidgetCount(Integer widgetCount) { this.widgetCount = widgetCount; }

        public Integer getTotalResources() { return totalResources; }
        public void setTotalResources(Integer totalResources) { this.totalResources = totalResources; }

        public Long getTotalSize() { return totalSize; }
        public void setTotalSize(Long totalSize) { this.totalSize = totalSize; }

        public String getExportVersion() { return exportVersion; }
        public void setExportVersion(String exportVersion) { this.exportVersion = exportVersion; }

        public String getExportedAt() { return exportedAt; }
        public void setExportedAt(String exportedAt) { this.exportedAt = exportedAt; }

        public Long getOriginalDashboardId() { return originalDashboardId; }
        public void setOriginalDashboardId(Long originalDashboardId) { this.originalDashboardId = originalDashboardId; }

        public List<ImportParserService.ResourceFileInfo> getResourceFiles() { return resourceFiles; }
        public void setResourceFiles(List<ImportParserService.ResourceFileInfo> resourceFiles) { this.resourceFiles = resourceFiles; }

        public java.util.Map<String, Integer> getResourceTypes() { return resourceTypes; }
        public void setResourceTypes(java.util.Map<String, Integer> resourceTypes) { this.resourceTypes = resourceTypes; }

        public java.util.Map<String, String> getSystemInfo() { return systemInfo; }
        public void setSystemInfo(java.util.Map<String, String> systemInfo) { this.systemInfo = systemInfo; }
    }
}
