package com.example.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportRequest {
    
    @NotNull(message = "大屏ID不能为空")
    private Long dashboardId;
    
    private String exportName; // 自定义导出名称
    
    private String description; // 导出描述
    
    private boolean includeImages = true; // 是否包含图片资源
    
    private boolean includeMaterials = true; // 是否包含素材资源
    
    private boolean includeVideos = true; // 是否包含视频资源
    
    private boolean includeHtmlCodes = true; // 是否包含HTML代码
    
    private boolean compressResources = true; // 是否压缩资源文件
    
    private String exportFormat = "ZIP"; // 导出格式，目前只支持ZIP
}
