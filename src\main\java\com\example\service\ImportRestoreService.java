package com.example.service;

import com.example.dto.ImportRequest;
import com.example.dto.ImportResult;
import com.example.entity.BiDashboard;
import com.example.entity.BiWidget;
import com.example.repository.BiDashboardRepository;
import com.example.repository.BiWidgetRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class ImportRestoreService {
    
    @Autowired
    private BiDashboardRepository dashboardRepository;
    
    @Autowired
    private BiWidgetRepository widgetRepository;
    
    @Autowired
    private FileUploadService fileUploadService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${upload.image.path}")
    private String imageUploadPath;
    
    @Value("${upload.material.path}")
    private String materialUploadPath;
    
    @Value("${upload.video.path}")
    private String videoUploadPath;
    
    @Value("${server.external-url:localhost:8080}")
    private String serverExternalUrl;
    
    // URL替换模式
    private static final Pattern URL_PATTERN = Pattern.compile("http://[^/]+/(images|materials|videos)/([^\\s\"']+)");
    
    /**
     * 恢复导入包到系统
     */
    @Transactional
    public ImportResult restoreImportPackage(ImportParserService.ImportPackage importPackage, 
                                           ImportRequest request) {
        log.info("开始恢复导入包: {}", importPackage.getDashboardName());
        
        try {
            // 1. 验证导入包
            validateImportPackage(importPackage);
            
            // 2. 检查同名大屏
            checkDashboardNameConflict(importPackage, request);
            
            // 3. 恢复资源文件
            ResourceRestoreResult resourceResult = restoreResourceFiles(importPackage, request);
            
            // 4. 创建大屏和组件
            BiDashboard newDashboard = createDashboardFromImport(importPackage, request, resourceResult);
            
            // 5. 生成导入结果
            ImportResult result = createImportResult(newDashboard, resourceResult);
            
            log.info("导入包恢复完成，新大屏ID: {}", newDashboard.getId());
            return result;
            
        } catch (Exception e) {
            log.error("恢复导入包失败", e);
            return ImportResult.failure("恢复导入包失败: " + e.getMessage(), 
                                      Arrays.asList(e.getMessage()));
        }
    }
    
    /**
     * 验证导入包
     */
    private void validateImportPackage(ImportParserService.ImportPackage importPackage) {
        if (importPackage.getDashboardConfig() == null) {
            throw new RuntimeException("导入包缺少大屏配置");
        }
        
        if (importPackage.getDashboardConfig().getName() == null || 
            importPackage.getDashboardConfig().getName().trim().isEmpty()) {
            throw new RuntimeException("大屏名称不能为空");
        }
        
        log.debug("导入包验证通过");
    }
    
    /**
     * 检查大屏名称冲突
     */
    private void checkDashboardNameConflict(ImportParserService.ImportPackage importPackage, 
                                          ImportRequest request) {
        String dashboardName = request.getNewDashboardName() != null ? 
                              request.getNewDashboardName() : 
                              importPackage.getDashboardConfig().getName();
        
        Optional<BiDashboard> existingDashboard = dashboardRepository.findByName(dashboardName);
        if (existingDashboard.isPresent()) {
            if (!request.isOverwriteExisting()) {
                throw new RuntimeException("大屏名称已存在: " + dashboardName + "，请选择覆盖或使用新名称");
            } else {
                log.info("将覆盖现有大屏: {}", dashboardName);
            }
        }
    }
    
    /**
     * 恢复资源文件
     */
    private ResourceRestoreResult restoreResourceFiles(ImportParserService.ImportPackage importPackage,
                                                      ImportRequest request) {
        log.info("开始恢复资源文件，总数: {}", importPackage.getTotalResources());
        
        ResourceRestoreResult result = new ResourceRestoreResult();
        Map<String, String> urlMappings = new HashMap<>();
        
        if (!request.isRestoreResources()) {
            log.info("跳过资源文件恢复");
            result.setUrlMappings(urlMappings);
            return result;
        }
        
        for (ImportParserService.ResourceFileInfo resourceFile : importPackage.getResourceFiles()) {
            try {
                String newUrl = restoreResourceFile(resourceFile, request);
                if (newUrl != null) {
                    // 查找原始URL
                    String originalUrl = findOriginalUrl(resourceFile, importPackage.getResourceMappings());
                    if (originalUrl != null) {
                        urlMappings.put(originalUrl, newUrl);
                    }
                    
                    result.incrementSuccessful();
                } else {
                    result.incrementSkipped();
                }
                
            } catch (Exception e) {
                log.warn("恢复资源文件失败: {}", resourceFile.getFileName(), e);
                result.incrementFailed();
                result.addError("恢复资源文件失败: " + resourceFile.getFileName() + " - " + e.getMessage());
            }
        }
        
        result.setUrlMappings(urlMappings);
        
        log.info("资源文件恢复完成，成功: {}, 跳过: {}, 失败: {}", 
                result.getSuccessfulCount(), result.getSkippedCount(), result.getFailedCount());
        
        return result;
    }
    
    /**
     * 恢复单个资源文件
     */
    private String restoreResourceFile(ImportParserService.ResourceFileInfo resourceFile,
                                     ImportRequest request) throws IOException {
        
        Path sourceFile = Paths.get(resourceFile.getFilePath());
        if (!Files.exists(sourceFile)) {
            log.warn("源文件不存在: {}", resourceFile.getFilePath());
            return null;
        }
        
        // 确定目标目录
        String targetDir = getTargetDirectory(resourceFile.getType(), request);
        Path targetDirPath = getAbsolutePath(targetDir);
        
        // 确保目标目录存在
        Files.createDirectories(targetDirPath);
        
        // 处理文件名冲突
        String targetFileName = resolveFileNameConflict(resourceFile.getFileName(), 
                                                       targetDirPath, request);
        
        Path targetFile = targetDirPath.resolve(targetFileName);
        
        // 复制文件
        Files.copy(sourceFile, targetFile, StandardCopyOption.REPLACE_EXISTING);
        
        // 生成新的URL
        String newUrl = generateResourceUrl(resourceFile.getType(), targetFileName);
        
        log.debug("恢复资源文件: {} -> {}", sourceFile, targetFile);
        return newUrl;
    }
    
    /**
     * 获取目标目录
     */
    private String getTargetDirectory(String resourceType, ImportRequest request) {
        String baseDir = null;
        
        switch (resourceType) {
            case "image":
                baseDir = imageUploadPath;
                break;
            case "material":
                baseDir = materialUploadPath;
                break;
            case "video":
                baseDir = videoUploadPath;
                break;
            default:
                throw new RuntimeException("不支持的资源类型: " + resourceType);
        }
        
        // 如果指定了资源路径前缀，使用前缀
        if (request.getResourcePathPrefix() != null && !request.getResourcePathPrefix().trim().isEmpty()) {
            return Paths.get(baseDir, request.getResourcePathPrefix()).toString();
        }
        
        return baseDir;
    }
    
    /**
     * 获取绝对路径
     */
    private Path getAbsolutePath(String configPath) {
        Path path = Paths.get(configPath);
        if (path.isAbsolute()) {
            return path.normalize();
        } else {
            String userDir = System.getProperty("user.dir");
            return Paths.get(userDir, configPath).toAbsolutePath().normalize();
        }
    }
    
    /**
     * 解决文件名冲突
     */
    private String resolveFileNameConflict(String originalFileName, Path targetDir, 
                                         ImportRequest request) throws IOException {
        
        Path targetFile = targetDir.resolve(originalFileName);
        
        if (!Files.exists(targetFile)) {
            return originalFileName;
        }
        
        // 根据冲突解决策略处理
        switch (request.getConflictResolution()) {
            case SKIP:
                log.debug("跳过已存在的文件: {}", originalFileName);
                return null;
                
            case OVERWRITE:
                log.debug("覆盖已存在的文件: {}", originalFileName);
                return originalFileName;
                
            case RENAME:
                return generateUniqueFileName(originalFileName, targetDir);
                
            default:
                return originalFileName;
        }
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFileName, Path targetDir) throws IOException {
        String baseName = getFileBaseName(originalFileName);
        String extension = getFileExtension(originalFileName);
        
        int counter = 1;
        String newFileName;
        
        do {
            newFileName = baseName + "_" + counter + extension;
            counter++;
        } while (Files.exists(targetDir.resolve(newFileName)));
        
        log.debug("生成唯一文件名: {} -> {}", originalFileName, newFileName);
        return newFileName;
    }
    
    /**
     * 获取文件基础名称（不含扩展名）
     */
    private String getFileBaseName(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return "";
    }
    
    /**
     * 生成资源URL
     */
    private String generateResourceUrl(String resourceType, String fileName) {
        String urlPath;
        switch (resourceType) {
            case "image":
                urlPath = "images";
                break;
            case "material":
                urlPath = "materials";
                break;
            case "video":
                urlPath = "videos";
                break;
            default:
                throw new RuntimeException("不支持的资源类型: " + resourceType);
        }
        
        return String.format("http://%s/%s/%s", serverExternalUrl, urlPath, fileName);
    }
    
    /**
     * 查找原始URL
     */
    private String findOriginalUrl(ImportParserService.ResourceFileInfo resourceFile,
                                 Map<String, String> resourceMappings) {
        String relativePath = resourceFile.getRelativePath();
        
        for (Map.Entry<String, String> entry : resourceMappings.entrySet()) {
            if (entry.getValue().equals(relativePath)) {
                return entry.getKey();
            }
        }
        
        return null;
    }

    /**
     * 从导入包创建大屏
     */
    private BiDashboard createDashboardFromImport(ImportParserService.ImportPackage importPackage,
                                                ImportRequest request,
                                                ResourceRestoreResult resourceResult) {

        ImportParserService.DashboardConfig dashboardConfig = importPackage.getDashboardConfig();

        // 处理同名大屏覆盖
        BiDashboard dashboard;
        String dashboardName = request.getNewDashboardName() != null ?
                              request.getNewDashboardName() : dashboardConfig.getName();

        Optional<BiDashboard> existingDashboard = dashboardRepository.findByName(dashboardName);
        if (existingDashboard.isPresent() && request.isOverwriteExisting()) {
            dashboard = existingDashboard.get();
            // 删除现有组件
            widgetRepository.deleteByDashboardId(dashboard.getId());
        } else {
            dashboard = new BiDashboard();
            dashboard.setCreatedAt(LocalDateTime.now());
        }

        // 设置大屏属性
        dashboard.setName(dashboardName);
        dashboard.setDescription(dashboardConfig.getDescription());
        dashboard.setUpdatedAt(LocalDateTime.now());

        // 更新画布配置中的资源URL
        String updatedCanvasConfig = updateResourceUrls(dashboardConfig.getCanvasConfig(),
                                                       resourceResult.getUrlMappings());
        dashboard.setCanvasConfig(updatedCanvasConfig);

        // 保存大屏
        dashboard = dashboardRepository.save(dashboard);

        // 创建组件
        createWidgetsFromImport(dashboard, dashboardConfig.getWidgets(), resourceResult);

        return dashboard;
    }

    /**
     * 从导入包创建组件
     */
    private void createWidgetsFromImport(BiDashboard dashboard,
                                       List<ImportParserService.WidgetConfig> widgetConfigs,
                                       ResourceRestoreResult resourceResult) {

        for (ImportParserService.WidgetConfig widgetConfig : widgetConfigs) {
            BiWidget widget = new BiWidget();
            widget.setDashboardId(dashboard.getId());
            widget.setWidgetType(widgetConfig.getWidgetType());
            widget.setZIndex(widgetConfig.getZIndex());
            widget.setRefreshSeconds(widgetConfig.getRefreshSeconds());
            widget.setCreatedAt(LocalDateTime.now());
            widget.setUpdatedAt(LocalDateTime.now());

            // 更新配置中的资源URL
            widget.setSetup(updateResourceUrls(widgetConfig.getSetup(), resourceResult.getUrlMappings()));
            widget.setData(updateResourceUrls(widgetConfig.getData(), resourceResult.getUrlMappings()));
            widget.setPosition(updateResourceUrls(widgetConfig.getPosition(), resourceResult.getUrlMappings()));
            widget.setOptions(updateResourceUrls(widgetConfig.getOptions(), resourceResult.getUrlMappings()));

            widgetRepository.save(widget);
        }

        log.info("创建组件完成，数量: {}", widgetConfigs.size());
    }

    /**
     * 更新配置中的资源URL
     */
    private String updateResourceUrls(String jsonConfig, Map<String, String> urlMappings) {
        if (jsonConfig == null || jsonConfig.trim().isEmpty() || urlMappings.isEmpty()) {
            return jsonConfig;
        }

        String updatedConfig = jsonConfig;

        for (Map.Entry<String, String> mapping : urlMappings.entrySet()) {
            String oldUrl = mapping.getKey();
            String newUrl = mapping.getValue();

            // 替换JSON中的URL引用
            updatedConfig = updatedConfig.replace("\"" + oldUrl + "\"", "\"" + newUrl + "\"");
            updatedConfig = updatedConfig.replace("'" + oldUrl + "'", "'" + newUrl + "'");

            // 处理HTML内容中的URL
            updatedConfig = updatedConfig.replace(oldUrl, newUrl);
        }

        return updatedConfig;
    }

    /**
     * 创建导入结果
     */
    private ImportResult createImportResult(BiDashboard dashboard, ResourceRestoreResult resourceResult) {
        ImportResult.ImportSummary summary = new ImportResult.ImportSummary();
        summary.setTotalResources(resourceResult.getTotalCount());
        summary.setSuccessfulResources(resourceResult.getSuccessfulCount());
        summary.setSkippedResources(resourceResult.getSkippedCount());
        summary.setFailedResources(resourceResult.getFailedCount());
        summary.setTotalSize(resourceResult.getTotalSize());

        // 设置资源类型统计
        Map<String, Integer> resourceTypes = new HashMap<>();
        resourceTypes.put("images", resourceResult.getImageCount());
        resourceTypes.put("materials", resourceResult.getMaterialCount());
        resourceTypes.put("videos", resourceResult.getVideoCount());
        summary.setResourceTypes(resourceTypes);

        return ImportResult.success(dashboard.getId(), dashboard.getName(), summary);
    }

    /**
     * 资源恢复结果类
     */
    public static class ResourceRestoreResult {
        private int totalCount = 0;
        private int successfulCount = 0;
        private int skippedCount = 0;
        private int failedCount = 0;
        private long totalSize = 0;
        private int imageCount = 0;
        private int materialCount = 0;
        private int videoCount = 0;
        private Map<String, String> urlMappings = new HashMap<>();
        private List<String> errors = new ArrayList<>();

        public void incrementSuccessful() { successfulCount++; totalCount++; }
        public void incrementSkipped() { skippedCount++; totalCount++; }
        public void incrementFailed() { failedCount++; totalCount++; }

        public void addError(String error) { errors.add(error); }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public int getSuccessfulCount() { return successfulCount; }
        public int getSkippedCount() { return skippedCount; }
        public int getFailedCount() { return failedCount; }
        public long getTotalSize() { return totalSize; }
        public void setTotalSize(long totalSize) { this.totalSize = totalSize; }
        public int getImageCount() { return imageCount; }
        public void setImageCount(int imageCount) { this.imageCount = imageCount; }
        public int getMaterialCount() { return materialCount; }
        public void setMaterialCount(int materialCount) { this.materialCount = materialCount; }
        public int getVideoCount() { return videoCount; }
        public void setVideoCount(int videoCount) { this.videoCount = videoCount; }
        public Map<String, String> getUrlMappings() { return urlMappings; }
        public void setUrlMappings(Map<String, String> urlMappings) { this.urlMappings = urlMappings; }
        public List<String> getErrors() { return errors; }
    }
}
