package com.example.controller;

import com.example.dto.ExportRequest;
import com.example.dto.ExportResult;
import com.example.service.BiDashboardExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/bi/dashboard/export")
@Slf4j
public class BiDashboardExportController {
    
    @Autowired
    private BiDashboardExportService exportService;
    
    /**
     * 开始导出大屏
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startExport(@Valid @RequestBody ExportRequest request) {
        try {
            log.info("收到导出请求，大屏ID: {}", request.getDashboardId());
            
            ExportResult result = exportService.startExport(request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "导出任务已启动");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("启动导出任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "启动导出任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 查询导出任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<Map<String, Object>> getExportStatus(@PathVariable String taskId) {
        try {
            ExportResult result = exportService.getExportStatus(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("查询导出状态失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询导出状态失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }
    
    /**
     * 获取大屏的所有导出任务
     */
    @GetMapping("/dashboard/{dashboardId}")
    public ResponseEntity<Map<String, Object>> getDashboardExportTasks(@PathVariable Long dashboardId) {
        try {
            List<ExportResult> results = exportService.getDashboardExportTasks(dashboardId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", results);
            response.put("total", results.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("查询大屏导出任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询导出任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 获取最近的导出任务
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentExportTasks(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<ExportResult> results = exportService.getRecentExportTasks(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", results);
            response.put("total", results.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("查询最近导出任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询最近导出任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 下载导出文件
     */
    @GetMapping("/{taskId}/download")
    public ResponseEntity<Resource> downloadExportFile(@PathVariable String taskId) {
        try {
            log.info("下载导出文件，任务ID: {}", taskId);
            
            String filePath = exportService.getExportFilePath(taskId);
            File file = new File(filePath);
            
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            String encodedFileName = URLEncoder.encode(file.getName(), StandardCharsets.UTF_8.toString());
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()));
            
            log.info("开始下载文件: {}, 大小: {} bytes", file.getName(), file.length());
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("下载导出文件失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 取消导出任务
     */
    @PostMapping("/{taskId}/cancel")
    public ResponseEntity<Map<String, Object>> cancelExport(@PathVariable String taskId) {
        try {
            boolean success = exportService.cancelExport(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "导出任务已取消" : "无法取消导出任务");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("取消导出任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "取消导出任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 删除导出任务和文件
     */
    @DeleteMapping("/{taskId}")
    public ResponseEntity<Map<String, Object>> deleteExport(@PathVariable String taskId) {
        try {
            boolean success = exportService.deleteExport(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "导出任务已删除" : "删除导出任务失败");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("删除导出任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除导出任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 清理过期的导出任务
     */
    @PostMapping("/cleanup/expired")
    public ResponseEntity<Map<String, Object>> cleanupExpiredTasks() {
        try {
            int cleanedCount = exportService.cleanupExpiredTasks();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "清理完成");
            response.put("cleanedCount", cleanedCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清理过期任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 清理旧的已完成任务
     */
    @PostMapping("/cleanup/old")
    public ResponseEntity<Map<String, Object>> cleanupOldCompletedTasks(
            @RequestParam(defaultValue = "30") int keepDays) {
        try {
            int cleanedCount = exportService.cleanupOldCompletedTasks(keepDays);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "清理完成");
            response.put("cleanedCount", cleanedCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清理旧任务失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "清理旧任务失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
