package com.example.repository;

import com.example.entity.BiDashboardExportTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BiDashboardExportTaskRepository extends JpaRepository<BiDashboardExportTask, Long> {
    
    /**
     * 根据任务ID查找导出任务
     */
    Optional<BiDashboardExportTask> findByTaskId(String taskId);
    
    /**
     * 根据大屏ID查找所有导出任务
     */
    List<BiDashboardExportTask> findByDashboardIdOrderByCreatedAtDesc(Long dashboardId);
    
    /**
     * 查找指定状态的导出任务
     */
    List<BiDashboardExportTask> findByStatusOrderByCreatedAtDesc(BiDashboardExportTask.ExportStatus status);
    
    /**
     * 查找已过期的导出任务
     */
    @Query("SELECT t FROM BiDashboardExportTask t WHERE t.expiresAt < :now")
    List<BiDashboardExportTask> findExpiredTasks(@Param("now") LocalDateTime now);
    
    /**
     * 查找需要清理的已完成任务（超过指定天数）
     */
    @Query("SELECT t FROM BiDashboardExportTask t WHERE t.status = 'COMPLETED' AND t.completedAt < :cutoffDate")
    List<BiDashboardExportTask> findCompletedTasksOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * 删除指定大屏的所有导出任务
     */
    void deleteByDashboardId(Long dashboardId);
    
    /**
     * 统计指定大屏的导出任务数量
     */
    long countByDashboardId(Long dashboardId);
    
    /**
     * 查找最近的导出任务
     */
    @Query("SELECT t FROM BiDashboardExportTask t ORDER BY t.createdAt DESC")
    List<BiDashboardExportTask> findRecentTasks();
}
