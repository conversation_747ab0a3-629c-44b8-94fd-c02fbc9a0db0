package com.example.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.reflect.Method;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig implements AsyncConfigurer {
    
    /**
     * 默认异步任务执行器
     */
    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(5);
        
        // 最大线程数
        executor.setMaxPoolSize(20);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Async-Task-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("异步任务执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
    
    /**
     * 导出任务专用执行器
     */
    @Bean(name = "exportTaskExecutor")
    public Executor exportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 导出任务通常比较耗时，使用较少的线程数
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("Export-Task-");
        executor.setKeepAliveSeconds(300); // 5分钟
        
        // 拒绝策略：抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(300); // 5分钟
        
        executor.initialize();
        
        log.info("导出任务执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
    
    /**
     * 导入任务专用执行器
     */
    @Bean(name = "importTaskExecutor")
    public Executor importTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 导入任务相对较快，可以使用更多线程
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(30);
        executor.setThreadNamePrefix("Import-Task-");
        executor.setKeepAliveSeconds(120); // 2分钟
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(180); // 3分钟
        
        executor.initialize();
        
        log.info("导入任务执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
    
    /**
     * 清理任务专用执行器
     */
    @Bean(name = "cleanupTaskExecutor")
    public Executor cleanupTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 清理任务优先级较低，使用单线程
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(2);
        executor.setQueueCapacity(10);
        executor.setThreadNamePrefix("Cleanup-Task-");
        executor.setKeepAliveSeconds(600); // 10分钟
        
        // 拒绝策略：丢弃最旧的任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("清理任务执行器初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
    
    /**
     * 异步任务异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new CustomAsyncExceptionHandler();
    }
    
    /**
     * 自定义异步异常处理器
     */
    public static class CustomAsyncExceptionHandler implements AsyncUncaughtExceptionHandler {
        
        @Override
        public void handleUncaughtException(Throwable throwable, Method method, Object... objects) {
            log.error("异步任务执行异常 - 方法: {}.{}, 参数: {}", 
                     method.getDeclaringClass().getSimpleName(), 
                     method.getName(), 
                     objects, 
                     throwable);
            
            // 可以在这里添加更多的异常处理逻辑，比如：
            // 1. 发送告警通知
            // 2. 记录到专门的错误日志
            // 3. 更新任务状态
            // 4. 清理资源等
        }
    }
    
    /**
     * 任务执行器监控信息
     */
    @Bean
    public TaskExecutorMonitor taskExecutorMonitor() {
        return new TaskExecutorMonitor();
    }
    
    /**
     * 任务执行器监控类
     */
    public static class TaskExecutorMonitor {
        
        public void logExecutorStatus(String executorName, ThreadPoolTaskExecutor executor) {
            if (executor != null) {
                ThreadPoolExecutor threadPool = executor.getThreadPoolExecutor();
                if (threadPool != null) {
                    log.info("执行器状态 [{}] - 活跃线程: {}, 池大小: {}, 队列大小: {}, 已完成任务: {}", 
                            executorName,
                            threadPool.getActiveCount(),
                            threadPool.getPoolSize(),
                            threadPool.getQueue().size(),
                            threadPool.getCompletedTaskCount());
                }
            }
        }
        
        public boolean isExecutorHealthy(ThreadPoolTaskExecutor executor) {
            if (executor == null) {
                return false;
            }
            
            ThreadPoolExecutor threadPool = executor.getThreadPoolExecutor();
            if (threadPool == null) {
                return false;
            }
            
            // 检查是否有过多的排队任务
            int queueSize = threadPool.getQueue().size();
            int maxQueueSize = executor.getQueueCapacity();
            
            if (queueSize > maxQueueSize * 0.8) {
                log.warn("执行器队列使用率过高: {}/{} ({}%)", 
                        queueSize, maxQueueSize, (queueSize * 100 / maxQueueSize));
                return false;
            }
            
            return true;
        }
    }
}
