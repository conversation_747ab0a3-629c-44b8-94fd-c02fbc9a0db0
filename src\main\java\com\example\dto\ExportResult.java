package com.example.dto;

import com.example.entity.BiDashboardExportTask;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportResult {
    
    private String taskId;
    
    private BiDashboardExportTask.ExportStatus status;
    
    private Integer progress;
    
    private String dashboardName;
    
    private String exportFileName;
    
    private Long exportFileSize;
    
    private String downloadUrl;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime completedAt;
    
    private LocalDateTime expiresAt;
    
    private String errorMessage;
    
    private ResourceSummary resourceSummary;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceSummary {
        private int totalFiles;
        private long totalSize;
        private int imageCount;
        private int materialCount;
        private int videoCount;
        private int htmlCodeCount;
        private Map<String, Object> details;
    }
    
    // 从ExportTask创建ExportResult
    public static ExportResult fromTask(BiDashboardExportTask task) {
        ExportResult result = new ExportResult();
        result.setTaskId(task.getTaskId());
        result.setStatus(task.getStatus());
        result.setProgress(task.getProgress());
        result.setDashboardName(task.getDashboardName());
        result.setExportFileName(task.getExportFileName());
        result.setExportFileSize(task.getExportFileSize());
        result.setCreatedAt(task.getCreatedAt());
        result.setCompletedAt(task.getCompletedAt());
        result.setExpiresAt(task.getExpiresAt());
        result.setErrorMessage(task.getErrorMessage());
        
        // 如果任务完成且文件存在，生成下载URL
        if (task.getStatus() == BiDashboardExportTask.ExportStatus.COMPLETED 
            && task.getExportFileName() != null) {
            result.setDownloadUrl("/api/bi/dashboard/export/" + task.getTaskId() + "/download");
        }
        
        return result;
    }
}
